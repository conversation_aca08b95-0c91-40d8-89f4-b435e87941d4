{% set search_query = request.q|default('') %}
<form method="get" action="{{ url }}" class="d-flex" role="search">
    <div class="input-group">
        <input type="text" 
               name="q" 
               value="{{ search_query }}" 
               class="form-control" 
               placeholder="{{ placeholder|default('Search...'|trans) }}" 
               aria-label="{{ 'Search'|trans }}">
        <button class="btn btn-outline-secondary" type="submit" aria-label="{{ 'Search'|trans }}">
            <i class="fas fa-search"></i>
        </button>
        {% if search_query %}
            <a href="{{ url }}" class="btn btn-outline-secondary" aria-label="{{ 'Clear search'|trans }}">
                <i class="fas fa-times"></i>
            </a>
        {% endif %}
    </div>
    {% for key, value in request %}
        {% if key != 'q' and value %}
            <input type="hidden" name="{{ key }}" value="{{ value }}">
        {% endif %}
    {% endfor %}
</form>
