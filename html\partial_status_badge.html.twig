{% import "macro_functions.html.twig" as mf %}

{% set status_lower = status|lower %}
{% set badge_class = 'bg-secondary' %}

{% if status_lower == 'active' %}
    {% set badge_class = 'bg-success' %}
{% elseif status_lower == 'pending' or status_lower == 'pending_setup' %}
    {% set badge_class = 'bg-warning' %}
{% elseif status_lower == 'suspended' or status_lower == 'failed' or status_lower == 'failed_setup' or status_lower == 'failed_renew' %}
    {% set badge_class = 'bg-danger' %}
{% elseif status_lower == 'canceled' or status_lower == 'cancelled' %}
    {% set badge_class = 'bg-dark' %}
{% elseif status_lower == 'paid' %}
    {% set badge_class = 'bg-success' %}
{% elseif status_lower == 'unpaid' %}
    {% set badge_class = 'bg-warning' %}
{% elseif status_lower == 'refunded' %}
    {% set badge_class = 'bg-info' %}
{% endif %}

<span class="badge {{ badge_class }} {{ class|default('') }}">
    {{ mf.status_name(status) }}
</span>
