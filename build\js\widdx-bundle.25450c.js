(()=>{var e={34:(e,t,n)=>{"use strict";var r=n(4901);e.exports=function(e){return"object"==typeof e?null!==e:r(e)}},81:(e,t,n)=>{"use strict";var r=n(9565),i=n(9306),o=n(8551),s=n(6823),a=n(851),l=TypeError;e.exports=function(e,t){var n=arguments.length<2?a(e):t;if(i(n))return o(r(n,e));throw new l(s(e)+" is not iterable")}},235:(e,t,n)=>{"use strict";var r=n(9213).forEach,i=n(4598)("forEach");e.exports=i?[].forEach:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}},283:(e,t,n)=>{"use strict";var r=n(9504),i=n(9039),o=n(4901),s=n(9297),a=n(3724),l=n(350).CONFIGURABLE,c=n(3706),u=n(1181),d=u.enforce,p=u.get,f=String,h=Object.defineProperty,g=r("".slice),m=r("".replace),v=r([].join),y=a&&!i(function(){return 8!==h(function(){},"length",{value:8}).length}),b=String(String).split("String"),_=e.exports=function(e,t,n){"Symbol("===g(f(t),0,7)&&(t="["+m(f(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),n&&n.getter&&(t="get "+t),n&&n.setter&&(t="set "+t),(!s(e,"name")||l&&e.name!==t)&&(a?h(e,"name",{value:t,configurable:!0}):e.name=t),y&&n&&s(n,"arity")&&e.length!==n.arity&&h(e,"length",{value:n.arity});try{n&&s(n,"constructor")&&n.constructor?a&&h(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var r=d(e);return s(r,"source")||(r.source=v(b,"string"==typeof t?t:"")),e};Function.prototype.toString=_(function(){return o(this)&&p(this).source||c(this)},"toString")},298:(e,t,n)=>{"use strict";var r=n(2195),i=n(5397),o=n(8480).f,s=n(7680),a="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(e){return a&&"Window"===r(e)?function(e){try{return o(e)}catch(e){return s(a)}}(e):o(i(e))}},350:(e,t,n)=>{"use strict";var r=n(3724),i=n(9297),o=Function.prototype,s=r&&Object.getOwnPropertyDescriptor,a=i(o,"name"),l=a&&"something"===function(){}.name,c=a&&(!r||r&&s(o,"name").configurable);e.exports={EXISTS:a,PROPER:l,CONFIGURABLE:c}},397:(e,t,n)=>{"use strict";var r=n(7751);e.exports=r("document","documentElement")},421:e=>{"use strict";e.exports={}},511:(e,t,n)=>{"use strict";var r=n(9167),i=n(9297),o=n(1951),s=n(4913).f;e.exports=function(e){var t=r.Symbol||(r.Symbol={});i(t,e)||s(t,e,{value:o.f(e)})}},597:(e,t,n)=>{"use strict";var r=n(9039),i=n(8227),o=n(9519),s=i("species");e.exports=function(e){return o>=51||!r(function(){var t=[];return(t.constructor={})[s]=function(){return{foo:1}},1!==t[e](Boolean).foo})}},616:(e,t,n)=>{"use strict";var r=n(9039);e.exports=!r(function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})},655:(e,t,n)=>{"use strict";var r=n(6955),i=String;e.exports=function(e){if("Symbol"===r(e))throw new TypeError("Cannot convert a Symbol value to a string");return i(e)}},687:(e,t,n)=>{"use strict";var r=n(4913).f,i=n(9297),o=n(8227)("toStringTag");e.exports=function(e,t,n){e&&!n&&(e=e.prototype),e&&!i(e,o)&&r(e,o,{configurable:!0,value:t})}},706:(e,t,n)=>{"use strict";var r=n(350).PROPER,i=n(9039),o=n(7452);e.exports=function(e){return i(function(){return!!o[e]()||"​᠎"!=="​᠎"[e]()||r&&o[e].name!==e})}},741:e=>{"use strict";var t=Math.ceil,n=Math.floor;e.exports=Math.trunc||function(e){var r=+e;return(r>0?n:t)(r)}},757:(e,t,n)=>{"use strict";var r=n(7751),i=n(4901),o=n(1625),s=n(7040),a=Object;e.exports=s?function(e){return"symbol"==typeof e}:function(e){var t=r("Symbol");return i(t)&&o(t.prototype,a(e))}},851:(e,t,n)=>{"use strict";var r=n(6955),i=n(5966),o=n(4117),s=n(6269),a=n(8227)("iterator");e.exports=function(e){if(!o(e))return i(e,a)||i(e,"@@iterator")||s[r(e)]}},1034:(e,t,n)=>{"use strict";var r=n(9565),i=n(9297),o=n(1625),s=n(5213),a=n(7979),l=RegExp.prototype;e.exports=s.correct?function(e){return e.flags}:function(e){return s.correct||!o(l,e)||i(e,"flags")?e.flags:r(a,e)}},1072:(e,t,n)=>{"use strict";var r=n(1828),i=n(8727);e.exports=Object.keys||function(e){return r(e,i)}},1088:(e,t,n)=>{"use strict";var r=n(6518),i=n(9565),o=n(6395),s=n(350),a=n(4901),l=n(3994),c=n(2787),u=n(2967),d=n(687),p=n(6699),f=n(6840),h=n(8227),g=n(6269),m=n(7657),v=s.PROPER,y=s.CONFIGURABLE,b=m.IteratorPrototype,_=m.BUGGY_SAFARI_ITERATORS,w=h("iterator"),x="keys",O="values",A="entries",S=function(){return this};e.exports=function(e,t,n,s,h,m,E){l(n,t,s);var C,T,k,L=function(e){if(e===h&&N)return N;if(!_&&e&&e in D)return D[e];switch(e){case x:case O:case A:return function(){return new n(this,e)}}return function(){return new n(this)}},j=t+" Iterator",I=!1,D=e.prototype,P=D[w]||D["@@iterator"]||h&&D[h],N=!_&&P||L(h),F="Array"===t&&D.entries||P;if(F&&(C=c(F.call(new e)))!==Object.prototype&&C.next&&(o||c(C)===b||(u?u(C,b):a(C[w])||f(C,w,S)),d(C,j,!0,!0),o&&(g[j]=S)),v&&h===O&&P&&P.name!==O&&(!o&&y?p(D,"name",O):(I=!0,N=function(){return i(P,this)})),h)if(T={values:L(O),keys:m?N:L(x),entries:L(A)},E)for(k in T)(_||I||!(k in D))&&f(D,k,T[k]);else r({target:t,proto:!0,forced:_||I},T);return o&&!E||D[w]===N||f(D,w,N,{name:h}),g[t]=N,T}},1181:(e,t,n)=>{"use strict";var r,i,o,s=n(8622),a=n(4576),l=n(34),c=n(6699),u=n(9297),d=n(7629),p=n(6119),f=n(421),h="Object already initialized",g=a.TypeError,m=a.WeakMap;if(s||d.state){var v=d.state||(d.state=new m);v.get=v.get,v.has=v.has,v.set=v.set,r=function(e,t){if(v.has(e))throw new g(h);return t.facade=e,v.set(e,t),t},i=function(e){return v.get(e)||{}},o=function(e){return v.has(e)}}else{var y=p("state");f[y]=!0,r=function(e,t){if(u(e,y))throw new g(h);return t.facade=e,c(e,y,t),t},i=function(e){return u(e,y)?e[y]:{}},o=function(e){return u(e,y)}}e.exports={set:r,get:i,has:o,enforce:function(e){return o(e)?i(e):r(e,{})},getterFor:function(e){return function(t){var n;if(!l(t)||(n=i(t)).type!==e)throw new g("Incompatible receiver, "+e+" required");return n}}}},1291:(e,t,n)=>{"use strict";var r=n(741);e.exports=function(e){var t=+e;return t!=t||0===t?0:r(t)}},1296:(e,t,n)=>{"use strict";var r=n(4495);e.exports=r&&!!Symbol.for&&!!Symbol.keyFor},1469:(e,t,n)=>{"use strict";var r=n(7433);e.exports=function(e,t){return new(r(e))(0===t?0:t)}},1510:(e,t,n)=>{"use strict";var r=n(6518),i=n(7751),o=n(9297),s=n(655),a=n(5745),l=n(1296),c=a("string-to-symbol-registry"),u=a("symbol-to-string-registry");r({target:"Symbol",stat:!0,forced:!l},{for:function(e){var t=s(e);if(o(c,t))return c[t];var n=i("Symbol")(t);return c[t]=n,u[n]=t,n}})},1625:(e,t,n)=>{"use strict";var r=n(9504);e.exports=r({}.isPrototypeOf)},1629:(e,t,n)=>{"use strict";var r=n(6518),i=n(235);r({target:"Array",proto:!0,forced:[].forEach!==i},{forEach:i})},1828:(e,t,n)=>{"use strict";var r=n(9504),i=n(9297),o=n(5397),s=n(9617).indexOf,a=n(421),l=r([].push);e.exports=function(e,t){var n,r=o(e),c=0,u=[];for(n in r)!i(a,n)&&i(r,n)&&l(u,n);for(;t.length>c;)i(r,n=t[c++])&&(~s(u,n)||l(u,n));return u}},1951:(e,t,n)=>{"use strict";var r=n(8227);t.f=r},2010:(e,t,n)=>{"use strict";var r=n(3724),i=n(350).EXISTS,o=n(9504),s=n(2106),a=Function.prototype,l=o(a.toString),c=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,u=o(c.exec);r&&!i&&s(a,"name",{configurable:!0,get:function(){try{return u(c,l(this))[1]}catch(e){return""}}})},2062:(e,t,n)=>{"use strict";var r=n(6518),i=n(9213).map;r({target:"Array",proto:!0,forced:!n(597)("map")},{map:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}})},2106:(e,t,n)=>{"use strict";var r=n(283),i=n(4913);e.exports=function(e,t,n){return n.get&&r(n.get,t,{getter:!0}),n.set&&r(n.set,t,{setter:!0}),i.f(e,t,n)}},2140:(e,t,n)=>{"use strict";var r={};r[n(8227)("toStringTag")]="z",e.exports="[object z]"===String(r)},2195:(e,t,n)=>{"use strict";var r=n(9504),i=r({}.toString),o=r("".slice);e.exports=function(e){return o(i(e),8,-1)}},2211:(e,t,n)=>{"use strict";var r=n(9039);e.exports=!r(function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})},2259:(e,t,n)=>{"use strict";n(511)("iterator")},2360:(e,t,n)=>{"use strict";var r,i=n(8551),o=n(6801),s=n(8727),a=n(421),l=n(397),c=n(4055),u=n(6119),d="prototype",p="script",f=u("IE_PROTO"),h=function(){},g=function(e){return"<"+p+">"+e+"</"+p+">"},m=function(e){e.write(g("")),e.close();var t=e.parentWindow.Object;return e=null,t},v=function(){try{r=new ActiveXObject("htmlfile")}catch(e){}var e,t,n;v="undefined"!=typeof document?document.domain&&r?m(r):(t=c("iframe"),n="java"+p+":",t.style.display="none",l.appendChild(t),t.src=String(n),(e=t.contentWindow.document).open(),e.write(g("document.F=Object")),e.close(),e.F):m(r);for(var i=s.length;i--;)delete v[d][s[i]];return v()};a[f]=!0,e.exports=Object.create||function(e,t){var n;return null!==e?(h[d]=i(e),n=new h,h[d]=null,n[f]=e):n=v(),void 0===t?n:o.f(n,t)}},2480:(e,t,n)=>{"use strict";n(5081)},2529:e=>{"use strict";e.exports=function(e,t){return{value:e,done:t}}},2675:(e,t,n)=>{"use strict";n(6761),n(1510),n(7812),n(3110),n(9773)},2762:(e,t,n)=>{"use strict";var r=n(6518),i=n(3802).trim;r({target:"String",proto:!0,forced:n(706)("trim")},{trim:function(){return i(this)}})},2777:(e,t,n)=>{"use strict";var r=n(9565),i=n(34),o=n(757),s=n(5966),a=n(4270),l=n(8227),c=TypeError,u=l("toPrimitive");e.exports=function(e,t){if(!i(e)||o(e))return e;var n,l=s(e,u);if(l){if(void 0===t&&(t="default"),n=r(l,e,t),!i(n)||o(n))return n;throw new c("Can't convert object to primitive value")}return void 0===t&&(t="number"),a(e,t)}},2787:(e,t,n)=>{"use strict";var r=n(9297),i=n(4901),o=n(8981),s=n(6119),a=n(2211),l=s("IE_PROTO"),c=Object,u=c.prototype;e.exports=a?c.getPrototypeOf:function(e){var t=o(e);if(r(t,l))return t[l];var n=t.constructor;return i(n)&&t instanceof n?n.prototype:t instanceof c?u:null}},2796:(e,t,n)=>{"use strict";var r=n(9039),i=n(4901),o=/#|\.prototype\./,s=function(e,t){var n=l[a(e)];return n===u||n!==c&&(i(t)?r(t):!!t)},a=s.normalize=function(e){return String(e).replace(o,".").toLowerCase()},l=s.data={},c=s.NATIVE="N",u=s.POLYFILL="P";e.exports=s},2839:(e,t,n)=>{"use strict";var r=n(4576).navigator,i=r&&r.userAgent;e.exports=i?String(i):""},2953:(e,t,n)=>{"use strict";var r=n(4576),i=n(7400),o=n(9296),s=n(3792),a=n(6699),l=n(687),c=n(8227)("iterator"),u=s.values,d=function(e,t){if(e){if(e[c]!==u)try{a(e,c,u)}catch(t){e[c]=u}if(l(e,t,!0),i[t])for(var n in s)if(e[n]!==s[n])try{a(e,n,s[n])}catch(t){e[n]=s[n]}}};for(var p in i)d(r[p]&&r[p].prototype,p);d(o,"DOMTokenList")},2967:(e,t,n)=>{"use strict";var r=n(6706),i=n(34),o=n(7750),s=n(3506);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=r(Object.prototype,"__proto__","set"))(n,[]),t=n instanceof Array}catch(e){}return function(n,r){return o(n),s(r),i(n)?(t?e(n,r):n.__proto__=r,n):n}}():void 0)},3110:(e,t,n)=>{"use strict";var r=n(6518),i=n(7751),o=n(8745),s=n(9565),a=n(9504),l=n(9039),c=n(4901),u=n(757),d=n(7680),p=n(6933),f=n(4495),h=String,g=i("JSON","stringify"),m=a(/./.exec),v=a("".charAt),y=a("".charCodeAt),b=a("".replace),_=a(1.1.toString),w=/[\uD800-\uDFFF]/g,x=/^[\uD800-\uDBFF]$/,O=/^[\uDC00-\uDFFF]$/,A=!f||l(function(){var e=i("Symbol")("stringify detection");return"[null]"!==g([e])||"{}"!==g({a:e})||"{}"!==g(Object(e))}),S=l(function(){return'"\\udf06\\ud834"'!==g("\udf06\ud834")||'"\\udead"'!==g("\udead")}),E=function(e,t){var n=d(arguments),r=p(t);if(c(r)||void 0!==e&&!u(e))return n[1]=function(e,t){if(c(r)&&(t=s(r,this,h(e),t)),!u(t))return t},o(g,null,n)},C=function(e,t,n){var r=v(n,t-1),i=v(n,t+1);return m(x,e)&&!m(O,i)||m(O,e)&&!m(x,r)?"\\u"+_(y(e,0),16):e};g&&r({target:"JSON",stat:!0,arity:3,forced:A||S},{stringify:function(e,t,n){var r=d(arguments),i=o(A?E:g,null,r);return S&&"string"==typeof i?b(i,w,C):i}})},3179:(e,t,n)=>{"use strict";var r=n(2140),i=n(6955);e.exports=r?{}.toString:function(){return"[object "+i(this)+"]"}},3288:(e,t,n)=>{"use strict";var r=n(9504),i=n(6840),o=Date.prototype,s="Invalid Date",a="toString",l=r(o[a]),c=r(o.getTime);String(new Date(NaN))!==s&&i(o,a,function(){var e=c(this);return e==e?l(this):s})},3392:(e,t,n)=>{"use strict";var r=n(9504),i=0,o=Math.random(),s=r(1.1.toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+s(++i+o,36)}},3418:(e,t,n)=>{"use strict";var r=n(6518),i=n(7916);r({target:"Array",stat:!0,forced:!n(4428)(function(e){Array.from(e)})},{from:i})},3500:(e,t,n)=>{"use strict";var r=n(4576),i=n(7400),o=n(9296),s=n(235),a=n(6699),l=function(e){if(e&&e.forEach!==s)try{a(e,"forEach",s)}catch(t){e.forEach=s}};for(var c in i)i[c]&&l(r[c]&&r[c].prototype);l(o)},3506:(e,t,n)=>{"use strict";var r=n(3925),i=String,o=TypeError;e.exports=function(e){if(r(e))return e;throw new o("Can't set "+i(e)+" as a prototype")}},3517:(e,t,n)=>{"use strict";var r=n(9504),i=n(9039),o=n(4901),s=n(6955),a=n(7751),l=n(3706),c=function(){},u=a("Reflect","construct"),d=/^\s*(?:class|function)\b/,p=r(d.exec),f=!d.test(c),h=function(e){if(!o(e))return!1;try{return u(c,[],e),!0}catch(e){return!1}},g=function(e){if(!o(e))return!1;switch(s(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return f||!!p(d,l(e))}catch(e){return!0}};g.sham=!0,e.exports=!u||i(function(){var e;return h(h.call)||!h(Object)||!h(function(){e=!0})||e})?g:h},3635:(e,t,n)=>{"use strict";var r=n(9039),i=n(4576).RegExp;e.exports=r(function(){var e=i(".","s");return!(e.dotAll&&e.test("\n")&&"s"===e.flags)})},3706:(e,t,n)=>{"use strict";var r=n(9504),i=n(4901),o=n(7629),s=r(Function.toString);i(o.inspectSource)||(o.inspectSource=function(e){return s(e)}),e.exports=o.inspectSource},3717:(e,t)=>{"use strict";t.f=Object.getOwnPropertySymbols},3724:(e,t,n)=>{"use strict";var r=n(9039);e.exports=!r(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})},3792:(e,t,n)=>{"use strict";var r=n(5397),i=n(6469),o=n(6269),s=n(1181),a=n(4913).f,l=n(1088),c=n(2529),u=n(6395),d=n(3724),p="Array Iterator",f=s.set,h=s.getterFor(p);e.exports=l(Array,"Array",function(e,t){f(this,{type:p,target:r(e),index:0,kind:t})},function(){var e=h(this),t=e.target,n=e.index++;if(!t||n>=t.length)return e.target=null,c(void 0,!0);switch(e.kind){case"keys":return c(n,!1);case"values":return c(t[n],!1)}return c([n,t[n]],!1)},"values");var g=o.Arguments=o.Array;if(i("keys"),i("values"),i("entries"),!u&&d&&"values"!==g.name)try{a(g,"name",{value:"values"})}catch(e){}},3802:(e,t,n)=>{"use strict";var r=n(9504),i=n(7750),o=n(655),s=n(7452),a=r("".replace),l=RegExp("^["+s+"]+"),c=RegExp("(^|[^"+s+"])["+s+"]+$"),u=function(e){return function(t){var n=o(i(t));return 1&e&&(n=a(n,l,"")),2&e&&(n=a(n,c,"$1")),n}};e.exports={start:u(1),end:u(2),trim:u(3)}},3925:(e,t,n)=>{"use strict";var r=n(34);e.exports=function(e){return r(e)||null===e}},3994:(e,t,n)=>{"use strict";var r=n(7657).IteratorPrototype,i=n(2360),o=n(6980),s=n(687),a=n(6269),l=function(){return this};e.exports=function(e,t,n,c){var u=t+" Iterator";return e.prototype=i(r,{next:o(+!c,n)}),s(e,u,!1,!0),a[u]=l,e}},4055:(e,t,n)=>{"use strict";var r=n(4576),i=n(34),o=r.document,s=i(o)&&i(o.createElement);e.exports=function(e){return s?o.createElement(e):{}}},4117:e=>{"use strict";e.exports=function(e){return null==e}},4209:(e,t,n)=>{"use strict";var r=n(8227),i=n(6269),o=r("iterator"),s=Array.prototype;e.exports=function(e){return void 0!==e&&(i.Array===e||s[o]===e)}},4270:(e,t,n)=>{"use strict";var r=n(9565),i=n(4901),o=n(34),s=TypeError;e.exports=function(e,t){var n,a;if("string"===t&&i(n=e.toString)&&!o(a=r(n,e)))return a;if(i(n=e.valueOf)&&!o(a=r(n,e)))return a;if("string"!==t&&i(n=e.toString)&&!o(a=r(n,e)))return a;throw new s("Can't convert object to primitive value")}},4346:(e,t,n)=>{"use strict";n(6518)({target:"Array",stat:!0},{isArray:n(4376)})},4376:(e,t,n)=>{"use strict";var r=n(2195);e.exports=Array.isArray||function(e){return"Array"===r(e)}},4428:(e,t,n)=>{"use strict";var r=n(8227)("iterator"),i=!1;try{var o=0,s={next:function(){return{done:!!o++}},return:function(){i=!0}};s[r]=function(){return this},Array.from(s,function(){throw 2})}catch(e){}e.exports=function(e,t){try{if(!t&&!i)return!1}catch(e){return!1}var n=!1;try{var o={};o[r]=function(){return{next:function(){return{done:n=!0}}}},e(o)}catch(e){}return n}},4495:(e,t,n)=>{"use strict";var r=n(9519),i=n(9039),o=n(4576).String;e.exports=!!Object.getOwnPropertySymbols&&!i(function(){var e=Symbol("symbol detection");return!o(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&r&&r<41})},4576:function(e,t,n){"use strict";var r=function(e){return e&&e.Math===Math&&e};e.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof n.g&&n.g)||r("object"==typeof this&&this)||function(){return this}()||Function("return this")()},4598:(e,t,n)=>{"use strict";var r=n(9039);e.exports=function(e,t){var n=[][e];return!!n&&r(function(){n.call(null,t||function(){return 1},1)})}},4659:(e,t,n)=>{"use strict";var r=n(3724),i=n(4913),o=n(6980);e.exports=function(e,t,n){r?i.f(e,t,o(0,n)):e[t]=n}},4692:function(e,t){var n;!function(t,n){"use strict";"object"==typeof e.exports?e.exports=t.document?n(t,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return n(e)}:n(t)}("undefined"!=typeof window?window:this,function(r,i){"use strict";var o=[],s=Object.getPrototypeOf,a=o.slice,l=o.flat?function(e){return o.flat.call(e)}:function(e){return o.concat.apply([],e)},c=o.push,u=o.indexOf,d={},p=d.toString,f=d.hasOwnProperty,h=f.toString,g=h.call(Object),m={},v=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},y=function(e){return null!=e&&e===e.window},b=r.document,_={type:!0,src:!0,nonce:!0,noModule:!0};function w(e,t,n){var r,i,o=(n=n||b).createElement("script");if(o.text=e,t)for(r in _)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function x(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?d[p.call(e)]||"object":typeof e}var O="3.7.1",A=/HTML$/i,S=function(e,t){return new S.fn.init(e,t)};function E(e){var t=!!e&&"length"in e&&e.length,n=x(e);return!v(e)&&!y(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}function C(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}S.fn=S.prototype={jquery:O,constructor:S,length:0,toArray:function(){return a.call(this)},get:function(e){return null==e?a.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=S.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return S.each(this,e)},map:function(e){return this.pushStack(S.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(S.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(S.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:c,sort:o.sort,splice:o.splice},S.extend=S.fn.extend=function(){var e,t,n,r,i,o,s=arguments[0]||{},a=1,l=arguments.length,c=!1;for("boolean"==typeof s&&(c=s,s=arguments[a]||{},a++),"object"==typeof s||v(s)||(s={}),a===l&&(s=this,a--);a<l;a++)if(null!=(e=arguments[a]))for(t in e)r=e[t],"__proto__"!==t&&s!==r&&(c&&r&&(S.isPlainObject(r)||(i=Array.isArray(r)))?(n=s[t],o=i&&!Array.isArray(n)?[]:i||S.isPlainObject(n)?n:{},i=!1,s[t]=S.extend(c,o,r)):void 0!==r&&(s[t]=r));return s},S.extend({expando:"jQuery"+(O+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==p.call(e))&&(!(t=s(e))||"function"==typeof(n=f.call(t,"constructor")&&t.constructor)&&h.call(n)===g)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){w(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(E(e))for(n=e.length;r<n&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},text:function(e){var t,n="",r=0,i=e.nodeType;if(!i)for(;t=e[r++];)n+=S.text(t);return 1===i||11===i?e.textContent:9===i?e.documentElement.textContent:3===i||4===i?e.nodeValue:n},makeArray:function(e,t){var n=t||[];return null!=e&&(E(Object(e))?S.merge(n,"string"==typeof e?[e]:e):c.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:u.call(t,e,n)},isXMLDoc:function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return!A.test(t||n&&n.nodeName||"HTML")},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,s=!n;i<o;i++)!t(e[i],i)!==s&&r.push(e[i]);return r},map:function(e,t,n){var r,i,o=0,s=[];if(E(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&s.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&s.push(i);return l(s)},guid:1,support:m}),"function"==typeof Symbol&&(S.fn[Symbol.iterator]=o[Symbol.iterator]),S.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){d["[object "+t+"]"]=t.toLowerCase()});var T=o.pop,k=o.sort,L=o.splice,j="[\\x20\\t\\r\\n\\f]",I=new RegExp("^"+j+"+|((?:^|[^\\\\])(?:\\\\.)*)"+j+"+$","g");S.contains=function(e,t){var n=t&&t.parentNode;return e===n||!(!n||1!==n.nodeType||!(e.contains?e.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))};var D=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function P(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}S.escapeSelector=function(e){return(e+"").replace(D,P)};var N=b,F=c;!function(){var e,t,n,i,s,l,c,d,p,h,g=F,v=S.expando,y=0,b=0,_=ee(),w=ee(),x=ee(),O=ee(),A=function(e,t){return e===t&&(s=!0),0},E="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",D="(?:\\\\[\\da-fA-F]{1,6}"+j+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",P="\\["+j+"*("+D+")(?:"+j+"*([*^$|!~]?=)"+j+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+D+"))|)"+j+"*\\]",M=":("+D+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+P+")*)|.*)\\)|)",$=new RegExp(j+"+","g"),q=new RegExp("^"+j+"*,"+j+"*"),H=new RegExp("^"+j+"*([>+~]|"+j+")"+j+"*"),R=new RegExp(j+"|>"),B=new RegExp(M),W=new RegExp("^"+D+"$"),z={ID:new RegExp("^#("+D+")"),CLASS:new RegExp("^\\.("+D+")"),TAG:new RegExp("^("+D+"|[*])"),ATTR:new RegExp("^"+P),PSEUDO:new RegExp("^"+M),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+j+"*(even|odd|(([+-]|)(\\d*)n|)"+j+"*(?:([+-]|)"+j+"*(\\d+)|))"+j+"*\\)|)","i"),bool:new RegExp("^(?:"+E+")$","i"),needsContext:new RegExp("^"+j+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+j+"*((?:-\\d)?\\d*)"+j+"*\\)|)(?=[^-]|$)","i")},V=/^(?:input|select|textarea|button)$/i,U=/^h\d$/i,X=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,K=/[+~]/,G=new RegExp("\\\\[\\da-fA-F]{1,6}"+j+"?|\\\\([^\\r\\n\\f])","g"),Q=function(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},Y=function(){le()},J=pe(function(e){return!0===e.disabled&&C(e,"fieldset")},{dir:"parentNode",next:"legend"});try{g.apply(o=a.call(N.childNodes),N.childNodes),o[N.childNodes.length].nodeType}catch(e){g={apply:function(e,t){F.apply(e,a.call(t))},call:function(e){F.apply(e,a.call(arguments,1))}}}function Z(e,t,n,r){var i,o,s,a,c,u,f,h=t&&t.ownerDocument,y=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==y&&9!==y&&11!==y)return n;if(!r&&(le(t),t=t||l,d)){if(11!==y&&(c=X.exec(e)))if(i=c[1]){if(9===y){if(!(s=t.getElementById(i)))return n;if(s.id===i)return g.call(n,s),n}else if(h&&(s=h.getElementById(i))&&Z.contains(t,s)&&s.id===i)return g.call(n,s),n}else{if(c[2])return g.apply(n,t.getElementsByTagName(e)),n;if((i=c[3])&&t.getElementsByClassName)return g.apply(n,t.getElementsByClassName(i)),n}if(!(O[e+" "]||p&&p.test(e))){if(f=e,h=t,1===y&&(R.test(e)||H.test(e))){for((h=K.test(e)&&ae(t.parentNode)||t)==t&&m.scope||((a=t.getAttribute("id"))?a=S.escapeSelector(a):t.setAttribute("id",a=v)),o=(u=ue(e)).length;o--;)u[o]=(a?"#"+a:":scope")+" "+de(u[o]);f=u.join(",")}try{return g.apply(n,h.querySelectorAll(f)),n}catch(t){O(e,!0)}finally{a===v&&t.removeAttribute("id")}}}return ye(e.replace(I,"$1"),t,n,r)}function ee(){var e=[];return function n(r,i){return e.push(r+" ")>t.cacheLength&&delete n[e.shift()],n[r+" "]=i}}function te(e){return e[v]=!0,e}function ne(e){var t=l.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function re(e){return function(t){return C(t,"input")&&t.type===e}}function ie(e){return function(t){return(C(t,"input")||C(t,"button"))&&t.type===e}}function oe(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&J(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function se(e){return te(function(t){return t=+t,te(function(n,r){for(var i,o=e([],n.length,t),s=o.length;s--;)n[i=o[s]]&&(n[i]=!(r[i]=n[i]))})})}function ae(e){return e&&void 0!==e.getElementsByTagName&&e}function le(e){var n,r=e?e.ownerDocument||e:N;return r!=l&&9===r.nodeType&&r.documentElement?(c=(l=r).documentElement,d=!S.isXMLDoc(l),h=c.matches||c.webkitMatchesSelector||c.msMatchesSelector,c.msMatchesSelector&&N!=l&&(n=l.defaultView)&&n.top!==n&&n.addEventListener("unload",Y),m.getById=ne(function(e){return c.appendChild(e).id=S.expando,!l.getElementsByName||!l.getElementsByName(S.expando).length}),m.disconnectedMatch=ne(function(e){return h.call(e,"*")}),m.scope=ne(function(){return l.querySelectorAll(":scope")}),m.cssHas=ne(function(){try{return l.querySelector(":has(*,:jqfake)"),!1}catch(e){return!0}}),m.getById?(t.filter.ID=function(e){var t=e.replace(G,Q);return function(e){return e.getAttribute("id")===t}},t.find.ID=function(e,t){if(void 0!==t.getElementById&&d){var n=t.getElementById(e);return n?[n]:[]}}):(t.filter.ID=function(e){var t=e.replace(G,Q);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},t.find.ID=function(e,t){if(void 0!==t.getElementById&&d){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(i=t.getElementsByName(e),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),t.find.TAG=function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):t.querySelectorAll(e)},t.find.CLASS=function(e,t){if(void 0!==t.getElementsByClassName&&d)return t.getElementsByClassName(e)},p=[],ne(function(e){var t;c.appendChild(e).innerHTML="<a id='"+v+"' href='' disabled='disabled'></a><select id='"+v+"-\r\\' disabled='disabled'><option selected=''></option></select>",e.querySelectorAll("[selected]").length||p.push("\\["+j+"*(?:value|"+E+")"),e.querySelectorAll("[id~="+v+"-]").length||p.push("~="),e.querySelectorAll("a#"+v+"+*").length||p.push(".#.+[+~]"),e.querySelectorAll(":checked").length||p.push(":checked"),(t=l.createElement("input")).setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),c.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&p.push(":enabled",":disabled"),(t=l.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||p.push("\\["+j+"*name"+j+"*="+j+"*(?:''|\"\")")}),m.cssHas||p.push(":has"),p=p.length&&new RegExp(p.join("|")),A=function(e,t){if(e===t)return s=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!m.sortDetached&&t.compareDocumentPosition(e)===n?e===l||e.ownerDocument==N&&Z.contains(N,e)?-1:t===l||t.ownerDocument==N&&Z.contains(N,t)?1:i?u.call(i,e)-u.call(i,t):0:4&n?-1:1)},l):l}for(e in Z.matches=function(e,t){return Z(e,null,null,t)},Z.matchesSelector=function(e,t){if(le(e),d&&!O[t+" "]&&(!p||!p.test(t)))try{var n=h.call(e,t);if(n||m.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){O(t,!0)}return Z(t,l,null,[e]).length>0},Z.contains=function(e,t){return(e.ownerDocument||e)!=l&&le(e),S.contains(e,t)},Z.attr=function(e,n){(e.ownerDocument||e)!=l&&le(e);var r=t.attrHandle[n.toLowerCase()],i=r&&f.call(t.attrHandle,n.toLowerCase())?r(e,n,!d):void 0;return void 0!==i?i:e.getAttribute(n)},Z.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},S.uniqueSort=function(e){var t,n=[],r=0,o=0;if(s=!m.sortStable,i=!m.sortStable&&a.call(e,0),k.call(e,A),s){for(;t=e[o++];)t===e[o]&&(r=n.push(o));for(;r--;)L.call(e,n[r],1)}return i=null,e},S.fn.uniqueSort=function(){return this.pushStack(S.uniqueSort(a.apply(this)))},t=S.expr={cacheLength:50,createPseudo:te,match:z,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(G,Q),e[3]=(e[3]||e[4]||e[5]||"").replace(G,Q),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||Z.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&Z.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return z.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&B.test(n)&&(t=ue(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(G,Q).toLowerCase();return"*"===e?function(){return!0}:function(e){return C(e,t)}},CLASS:function(e){var t=_[e+" "];return t||(t=new RegExp("(^|"+j+")"+e+"("+j+"|$)"))&&_(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,t,n){return function(r){var i=Z.attr(r,e);return null==i?"!="===t:!t||(i+="","="===t?i===n:"!="===t?i!==n:"^="===t?n&&0===i.indexOf(n):"*="===t?n&&i.indexOf(n)>-1:"$="===t?n&&i.slice(-n.length)===n:"~="===t?(" "+i.replace($," ")+" ").indexOf(n)>-1:"|="===t&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,r,i){var o="nth"!==e.slice(0,3),s="last"!==e.slice(-4),a="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,l){var c,u,d,p,f,h=o!==s?"nextSibling":"previousSibling",g=t.parentNode,m=a&&t.nodeName.toLowerCase(),b=!l&&!a,_=!1;if(g){if(o){for(;h;){for(d=t;d=d[h];)if(a?C(d,m):1===d.nodeType)return!1;f=h="only"===e&&!f&&"nextSibling"}return!0}if(f=[s?g.firstChild:g.lastChild],s&&b){for(_=(p=(c=(u=g[v]||(g[v]={}))[e]||[])[0]===y&&c[1])&&c[2],d=p&&g.childNodes[p];d=++p&&d&&d[h]||(_=p=0)||f.pop();)if(1===d.nodeType&&++_&&d===t){u[e]=[y,p,_];break}}else if(b&&(_=p=(c=(u=t[v]||(t[v]={}))[e]||[])[0]===y&&c[1]),!1===_)for(;(d=++p&&d&&d[h]||(_=p=0)||f.pop())&&(!(a?C(d,m):1===d.nodeType)||!++_||(b&&((u=d[v]||(d[v]={}))[e]=[y,_]),d!==t)););return(_-=i)===r||_%r===0&&_/r>=0}}},PSEUDO:function(e,n){var r,i=t.pseudos[e]||t.setFilters[e.toLowerCase()]||Z.error("unsupported pseudo: "+e);return i[v]?i(n):i.length>1?(r=[e,e,"",n],t.setFilters.hasOwnProperty(e.toLowerCase())?te(function(e,t){for(var r,o=i(e,n),s=o.length;s--;)e[r=u.call(e,o[s])]=!(t[r]=o[s])}):function(e){return i(e,0,r)}):i}},pseudos:{not:te(function(e){var t=[],n=[],r=ve(e.replace(I,"$1"));return r[v]?te(function(e,t,n,i){for(var o,s=r(e,null,i,[]),a=e.length;a--;)(o=s[a])&&(e[a]=!(t[a]=o))}):function(e,i,o){return t[0]=e,r(t,null,o,n),t[0]=null,!n.pop()}}),has:te(function(e){return function(t){return Z(e,t).length>0}}),contains:te(function(e){return e=e.replace(G,Q),function(t){return(t.textContent||S.text(t)).indexOf(e)>-1}}),lang:te(function(e){return W.test(e||"")||Z.error("unsupported lang: "+e),e=e.replace(G,Q).toLowerCase(),function(t){var n;do{if(n=d?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(e){var t=r.location&&r.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===c},focus:function(e){return e===function(){try{return l.activeElement}catch(e){}}()&&l.hasFocus()&&!!(e.type||e.href||~e.tabIndex)},enabled:oe(!1),disabled:oe(!0),checked:function(e){return C(e,"input")&&!!e.checked||C(e,"option")&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!t.pseudos.empty(e)},header:function(e){return U.test(e.nodeName)},input:function(e){return V.test(e.nodeName)},button:function(e){return C(e,"input")&&"button"===e.type||C(e,"button")},text:function(e){var t;return C(e,"input")&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:se(function(){return[0]}),last:se(function(e,t){return[t-1]}),eq:se(function(e,t,n){return[n<0?n+t:n]}),even:se(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:se(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:se(function(e,t,n){var r;for(r=n<0?n+t:n>t?t:n;--r>=0;)e.push(r);return e}),gt:se(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}},t.pseudos.nth=t.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})t.pseudos[e]=re(e);for(e in{submit:!0,reset:!0})t.pseudos[e]=ie(e);function ce(){}function ue(e,n){var r,i,o,s,a,l,c,u=w[e+" "];if(u)return n?0:u.slice(0);for(a=e,l=[],c=t.preFilter;a;){for(s in r&&!(i=q.exec(a))||(i&&(a=a.slice(i[0].length)||a),l.push(o=[])),r=!1,(i=H.exec(a))&&(r=i.shift(),o.push({value:r,type:i[0].replace(I," ")}),a=a.slice(r.length)),t.filter)!(i=z[s].exec(a))||c[s]&&!(i=c[s](i))||(r=i.shift(),o.push({value:r,type:s,matches:i}),a=a.slice(r.length));if(!r)break}return n?a.length:a?Z.error(e):w(e,l).slice(0)}function de(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function pe(e,t,n){var r=t.dir,i=t.next,o=i||r,s=n&&"parentNode"===o,a=b++;return t.first?function(t,n,i){for(;t=t[r];)if(1===t.nodeType||s)return e(t,n,i);return!1}:function(t,n,l){var c,u,d=[y,a];if(l){for(;t=t[r];)if((1===t.nodeType||s)&&e(t,n,l))return!0}else for(;t=t[r];)if(1===t.nodeType||s)if(u=t[v]||(t[v]={}),i&&C(t,i))t=t[r]||t;else{if((c=u[o])&&c[0]===y&&c[1]===a)return d[2]=c[2];if(u[o]=d,d[2]=e(t,n,l))return!0}return!1}}function fe(e){return e.length>1?function(t,n,r){for(var i=e.length;i--;)if(!e[i](t,n,r))return!1;return!0}:e[0]}function he(e,t,n,r,i){for(var o,s=[],a=0,l=e.length,c=null!=t;a<l;a++)(o=e[a])&&(n&&!n(o,r,i)||(s.push(o),c&&t.push(a)));return s}function ge(e,t,n,r,i,o){return r&&!r[v]&&(r=ge(r)),i&&!i[v]&&(i=ge(i,o)),te(function(o,s,a,l){var c,d,p,f,h=[],m=[],v=s.length,y=o||function(e,t,n){for(var r=0,i=t.length;r<i;r++)Z(e,t[r],n);return n}(t||"*",a.nodeType?[a]:a,[]),b=!e||!o&&t?y:he(y,h,e,a,l);if(n?n(b,f=i||(o?e:v||r)?[]:s,a,l):f=b,r)for(c=he(f,m),r(c,[],a,l),d=c.length;d--;)(p=c[d])&&(f[m[d]]=!(b[m[d]]=p));if(o){if(i||e){if(i){for(c=[],d=f.length;d--;)(p=f[d])&&c.push(b[d]=p);i(null,f=[],c,l)}for(d=f.length;d--;)(p=f[d])&&(c=i?u.call(o,p):h[d])>-1&&(o[c]=!(s[c]=p))}}else f=he(f===s?f.splice(v,f.length):f),i?i(null,s,f,l):g.apply(s,f)})}function me(e){for(var r,i,o,s=e.length,a=t.relative[e[0].type],l=a||t.relative[" "],c=a?1:0,d=pe(function(e){return e===r},l,!0),p=pe(function(e){return u.call(r,e)>-1},l,!0),f=[function(e,t,i){var o=!a&&(i||t!=n)||((r=t).nodeType?d(e,t,i):p(e,t,i));return r=null,o}];c<s;c++)if(i=t.relative[e[c].type])f=[pe(fe(f),i)];else{if((i=t.filter[e[c].type].apply(null,e[c].matches))[v]){for(o=++c;o<s&&!t.relative[e[o].type];o++);return ge(c>1&&fe(f),c>1&&de(e.slice(0,c-1).concat({value:" "===e[c-2].type?"*":""})).replace(I,"$1"),i,c<o&&me(e.slice(c,o)),o<s&&me(e=e.slice(o)),o<s&&de(e))}f.push(i)}return fe(f)}function ve(e,r){var i,o=[],s=[],a=x[e+" "];if(!a){for(r||(r=ue(e)),i=r.length;i--;)(a=me(r[i]))[v]?o.push(a):s.push(a);a=x(e,function(e,r){var i=r.length>0,o=e.length>0,s=function(s,a,c,u,p){var f,h,m,v=0,b="0",_=s&&[],w=[],x=n,O=s||o&&t.find.TAG("*",p),A=y+=null==x?1:Math.random()||.1,E=O.length;for(p&&(n=a==l||a||p);b!==E&&null!=(f=O[b]);b++){if(o&&f){for(h=0,a||f.ownerDocument==l||(le(f),c=!d);m=e[h++];)if(m(f,a||l,c)){g.call(u,f);break}p&&(y=A)}i&&((f=!m&&f)&&v--,s&&_.push(f))}if(v+=b,i&&b!==v){for(h=0;m=r[h++];)m(_,w,a,c);if(s){if(v>0)for(;b--;)_[b]||w[b]||(w[b]=T.call(u));w=he(w)}g.apply(u,w),p&&!s&&w.length>0&&v+r.length>1&&S.uniqueSort(u)}return p&&(y=A,n=x),_};return i?te(s):s}(s,o)),a.selector=e}return a}function ye(e,n,r,i){var o,s,a,l,c,u="function"==typeof e&&e,p=!i&&ue(e=u.selector||e);if(r=r||[],1===p.length){if((s=p[0]=p[0].slice(0)).length>2&&"ID"===(a=s[0]).type&&9===n.nodeType&&d&&t.relative[s[1].type]){if(!(n=(t.find.ID(a.matches[0].replace(G,Q),n)||[])[0]))return r;u&&(n=n.parentNode),e=e.slice(s.shift().value.length)}for(o=z.needsContext.test(e)?0:s.length;o--&&(a=s[o],!t.relative[l=a.type]);)if((c=t.find[l])&&(i=c(a.matches[0].replace(G,Q),K.test(s[0].type)&&ae(n.parentNode)||n))){if(s.splice(o,1),!(e=i.length&&de(s)))return g.apply(r,i),r;break}}return(u||ve(e,p))(i,n,!d,r,!n||K.test(e)&&ae(n.parentNode)||n),r}ce.prototype=t.filters=t.pseudos,t.setFilters=new ce,m.sortStable=v.split("").sort(A).join("")===v,le(),m.sortDetached=ne(function(e){return 1&e.compareDocumentPosition(l.createElement("fieldset"))}),S.find=Z,S.expr[":"]=S.expr.pseudos,S.unique=S.uniqueSort,Z.compile=ve,Z.select=ye,Z.setDocument=le,Z.tokenize=ue,Z.escape=S.escapeSelector,Z.getText=S.text,Z.isXML=S.isXMLDoc,Z.selectors=S.expr,Z.support=S.support,Z.uniqueSort=S.uniqueSort}();var M=function(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&S(e).is(n))break;r.push(e)}return r},$=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},q=S.expr.match.needsContext,H=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function R(e,t,n){return v(t)?S.grep(e,function(e,r){return!!t.call(e,r,e)!==n}):t.nodeType?S.grep(e,function(e){return e===t!==n}):"string"!=typeof t?S.grep(e,function(e){return u.call(t,e)>-1!==n}):S.filter(t,e,n)}S.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?S.find.matchesSelector(r,e)?[r]:[]:S.find.matches(e,S.grep(t,function(e){return 1===e.nodeType}))},S.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(S(e).filter(function(){for(t=0;t<r;t++)if(S.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)S.find(e,i[t],n);return r>1?S.uniqueSort(n):n},filter:function(e){return this.pushStack(R(this,e||[],!1))},not:function(e){return this.pushStack(R(this,e||[],!0))},is:function(e){return!!R(this,"string"==typeof e&&q.test(e)?S(e):e||[],!1).length}});var B,W=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(S.fn.init=function(e,t,n){var r,i;if(!e)return this;if(n=n||B,"string"==typeof e){if(!(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:W.exec(e))||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof S?t[0]:t,S.merge(this,S.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:b,!0)),H.test(r[1])&&S.isPlainObject(t))for(r in t)v(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(i=b.getElementById(r[2]))&&(this[0]=i,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):v(e)?void 0!==n.ready?n.ready(e):e(S):S.makeArray(e,this)}).prototype=S.fn,B=S(b);var z=/^(?:parents|prev(?:Until|All))/,V={children:!0,contents:!0,next:!0,prev:!0};function U(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}S.fn.extend({has:function(e){var t=S(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(S.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],s="string"!=typeof e&&S(e);if(!q.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?s.index(n)>-1:1===n.nodeType&&S.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?S.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?u.call(S(e),this[0]):u.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(S.uniqueSort(S.merge(this.get(),S(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),S.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return M(e,"parentNode")},parentsUntil:function(e,t,n){return M(e,"parentNode",n)},next:function(e){return U(e,"nextSibling")},prev:function(e){return U(e,"previousSibling")},nextAll:function(e){return M(e,"nextSibling")},prevAll:function(e){return M(e,"previousSibling")},nextUntil:function(e,t,n){return M(e,"nextSibling",n)},prevUntil:function(e,t,n){return M(e,"previousSibling",n)},siblings:function(e){return $((e.parentNode||{}).firstChild,e)},children:function(e){return $(e.firstChild)},contents:function(e){return null!=e.contentDocument&&s(e.contentDocument)?e.contentDocument:(C(e,"template")&&(e=e.content||e),S.merge([],e.childNodes))}},function(e,t){S.fn[e]=function(n,r){var i=S.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=S.filter(r,i)),this.length>1&&(V[e]||S.uniqueSort(i),z.test(e)&&i.reverse()),this.pushStack(i)}});var X=/[^\x20\t\r\n\f]+/g;function K(e){return e}function G(e){throw e}function Q(e,t,n,r){var i;try{e&&v(i=e.promise)?i.call(e).done(t).fail(n):e&&v(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}S.Callbacks=function(e){e="string"==typeof e?function(e){var t={};return S.each(e.match(X)||[],function(e,n){t[n]=!0}),t}(e):S.extend({},e);var t,n,r,i,o=[],s=[],a=-1,l=function(){for(i=i||e.once,r=t=!0;s.length;a=-1)for(n=s.shift();++a<o.length;)!1===o[a].apply(n[0],n[1])&&e.stopOnFalse&&(a=o.length,n=!1);e.memory||(n=!1),t=!1,i&&(o=n?[]:"")},c={add:function(){return o&&(n&&!t&&(a=o.length-1,s.push(n)),function t(n){S.each(n,function(n,r){v(r)?e.unique&&c.has(r)||o.push(r):r&&r.length&&"string"!==x(r)&&t(r)})}(arguments),n&&!t&&l()),this},remove:function(){return S.each(arguments,function(e,t){for(var n;(n=S.inArray(t,o,n))>-1;)o.splice(n,1),n<=a&&a--}),this},has:function(e){return e?S.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return i=s=[],o=n="",this},disabled:function(){return!o},lock:function(){return i=s=[],n||t||(o=n=""),this},locked:function(){return!!i},fireWith:function(e,n){return i||(n=[e,(n=n||[]).slice?n.slice():n],s.push(n),t||l()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!r}};return c},S.extend({Deferred:function(e){var t=[["notify","progress",S.Callbacks("memory"),S.Callbacks("memory"),2],["resolve","done",S.Callbacks("once memory"),S.Callbacks("once memory"),0,"resolved"],["reject","fail",S.Callbacks("once memory"),S.Callbacks("once memory"),1,"rejected"]],n="pending",i={state:function(){return n},always:function(){return o.done(arguments).fail(arguments),this},catch:function(e){return i.then(null,e)},pipe:function(){var e=arguments;return S.Deferred(function(n){S.each(t,function(t,r){var i=v(e[r[4]])&&e[r[4]];o[r[1]](function(){var e=i&&i.apply(this,arguments);e&&v(e.promise)?e.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[r[0]+"With"](this,i?[e]:arguments)})}),e=null}).promise()},then:function(e,n,i){var o=0;function s(e,t,n,i){return function(){var a=this,l=arguments,c=function(){var r,c;if(!(e<o)){if((r=n.apply(a,l))===t.promise())throw new TypeError("Thenable self-resolution");c=r&&("object"==typeof r||"function"==typeof r)&&r.then,v(c)?i?c.call(r,s(o,t,K,i),s(o,t,G,i)):(o++,c.call(r,s(o,t,K,i),s(o,t,G,i),s(o,t,K,t.notifyWith))):(n!==K&&(a=void 0,l=[r]),(i||t.resolveWith)(a,l))}},u=i?c:function(){try{c()}catch(r){S.Deferred.exceptionHook&&S.Deferred.exceptionHook(r,u.error),e+1>=o&&(n!==G&&(a=void 0,l=[r]),t.rejectWith(a,l))}};e?u():(S.Deferred.getErrorHook?u.error=S.Deferred.getErrorHook():S.Deferred.getStackHook&&(u.error=S.Deferred.getStackHook()),r.setTimeout(u))}}return S.Deferred(function(r){t[0][3].add(s(0,r,v(i)?i:K,r.notifyWith)),t[1][3].add(s(0,r,v(e)?e:K)),t[2][3].add(s(0,r,v(n)?n:G))}).promise()},promise:function(e){return null!=e?S.extend(e,i):i}},o={};return S.each(t,function(e,r){var s=r[2],a=r[5];i[r[1]]=s.add,a&&s.add(function(){n=a},t[3-e][2].disable,t[3-e][3].disable,t[0][2].lock,t[0][3].lock),s.add(r[3].fire),o[r[0]]=function(){return o[r[0]+"With"](this===o?void 0:this,arguments),this},o[r[0]+"With"]=s.fireWith}),i.promise(o),e&&e.call(o,o),o},when:function(e){var t=arguments.length,n=t,r=Array(n),i=a.call(arguments),o=S.Deferred(),s=function(e){return function(n){r[e]=this,i[e]=arguments.length>1?a.call(arguments):n,--t||o.resolveWith(r,i)}};if(t<=1&&(Q(e,o.done(s(n)).resolve,o.reject,!t),"pending"===o.state()||v(i[n]&&i[n].then)))return o.then();for(;n--;)Q(i[n],s(n),o.reject);return o.promise()}});var Y=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;S.Deferred.exceptionHook=function(e,t){r.console&&r.console.warn&&e&&Y.test(e.name)&&r.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},S.readyException=function(e){r.setTimeout(function(){throw e})};var J=S.Deferred();function Z(){b.removeEventListener("DOMContentLoaded",Z),r.removeEventListener("load",Z),S.ready()}S.fn.ready=function(e){return J.then(e).catch(function(e){S.readyException(e)}),this},S.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--S.readyWait:S.isReady)||(S.isReady=!0,!0!==e&&--S.readyWait>0||J.resolveWith(b,[S]))}}),S.ready.then=J.then,"complete"===b.readyState||"loading"!==b.readyState&&!b.documentElement.doScroll?r.setTimeout(S.ready):(b.addEventListener("DOMContentLoaded",Z),r.addEventListener("load",Z));var ee=function(e,t,n,r,i,o,s){var a=0,l=e.length,c=null==n;if("object"===x(n))for(a in i=!0,n)ee(e,t,a,n[a],!0,o,s);else if(void 0!==r&&(i=!0,v(r)||(s=!0),c&&(s?(t.call(e,r),t=null):(c=t,t=function(e,t,n){return c.call(S(e),n)})),t))for(;a<l;a++)t(e[a],n,s?r:r.call(e[a],a,t(e[a],n)));return i?e:c?t.call(e):l?t(e[0],n):o},te=/^-ms-/,ne=/-([a-z])/g;function re(e,t){return t.toUpperCase()}function ie(e){return e.replace(te,"ms-").replace(ne,re)}var oe=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function se(){this.expando=S.expando+se.uid++}se.uid=1,se.prototype={cache:function(e){var t=e[this.expando];return t||(t={},oe(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[ie(t)]=n;else for(r in t)i[ie(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][ie(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(ie):(t=ie(t))in r?[t]:t.match(X)||[]).length;for(;n--;)delete r[t[n]]}(void 0===t||S.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!S.isEmptyObject(t)}};var ae=new se,le=new se,ce=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ue=/[A-Z]/g;function de(e,t,n){var r;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(ue,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n=function(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:ce.test(e)?JSON.parse(e):e)}(n)}catch(e){}le.set(e,t,n)}else n=void 0;return n}S.extend({hasData:function(e){return le.hasData(e)||ae.hasData(e)},data:function(e,t,n){return le.access(e,t,n)},removeData:function(e,t){le.remove(e,t)},_data:function(e,t,n){return ae.access(e,t,n)},_removeData:function(e,t){ae.remove(e,t)}}),S.fn.extend({data:function(e,t){var n,r,i,o=this[0],s=o&&o.attributes;if(void 0===e){if(this.length&&(i=le.get(o),1===o.nodeType&&!ae.get(o,"hasDataAttrs"))){for(n=s.length;n--;)s[n]&&0===(r=s[n].name).indexOf("data-")&&(r=ie(r.slice(5)),de(o,r,i[r]));ae.set(o,"hasDataAttrs",!0)}return i}return"object"==typeof e?this.each(function(){le.set(this,e)}):ee(this,function(t){var n;if(o&&void 0===t)return void 0!==(n=le.get(o,e))||void 0!==(n=de(o,e))?n:void 0;this.each(function(){le.set(this,e,t)})},null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){le.remove(this,e)})}}),S.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=ae.get(e,t),n&&(!r||Array.isArray(n)?r=ae.access(e,t,S.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=S.queue(e,t),r=n.length,i=n.shift(),o=S._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,function(){S.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return ae.get(e,n)||ae.access(e,n,{empty:S.Callbacks("once memory").add(function(){ae.remove(e,[t+"queue",n])})})}}),S.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?S.queue(this[0],e):void 0===t?this:this.each(function(){var n=S.queue(this,e,t);S._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&S.dequeue(this,e)})},dequeue:function(e){return this.each(function(){S.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=S.Deferred(),o=this,s=this.length,a=function(){--r||i.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(n=ae.get(o[s],e+"queueHooks"))&&n.empty&&(r++,n.empty.add(a));return a(),i.promise(t)}});var pe=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,fe=new RegExp("^(?:([+-])=|)("+pe+")([a-z%]*)$","i"),he=["Top","Right","Bottom","Left"],ge=b.documentElement,me=function(e){return S.contains(e.ownerDocument,e)},ve={composed:!0};ge.getRootNode&&(me=function(e){return S.contains(e.ownerDocument,e)||e.getRootNode(ve)===e.ownerDocument});var ye=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&me(e)&&"none"===S.css(e,"display")};function be(e,t,n,r){var i,o,s=20,a=r?function(){return r.cur()}:function(){return S.css(e,t,"")},l=a(),c=n&&n[3]||(S.cssNumber[t]?"":"px"),u=e.nodeType&&(S.cssNumber[t]||"px"!==c&&+l)&&fe.exec(S.css(e,t));if(u&&u[3]!==c){for(l/=2,c=c||u[3],u=+l||1;s--;)S.style(e,t,u+c),(1-o)*(1-(o=a()/l||.5))<=0&&(s=0),u/=o;u*=2,S.style(e,t,u+c),n=n||[]}return n&&(u=+u||+l||0,i=n[1]?u+(n[1]+1)*n[2]:+n[2],r&&(r.unit=c,r.start=u,r.end=i)),i}var _e={};function we(e){var t,n=e.ownerDocument,r=e.nodeName,i=_e[r];return i||(t=n.body.appendChild(n.createElement(r)),i=S.css(t,"display"),t.parentNode.removeChild(t),"none"===i&&(i="block"),_e[r]=i,i)}function xe(e,t){for(var n,r,i=[],o=0,s=e.length;o<s;o++)(r=e[o]).style&&(n=r.style.display,t?("none"===n&&(i[o]=ae.get(r,"display")||null,i[o]||(r.style.display="")),""===r.style.display&&ye(r)&&(i[o]=we(r))):"none"!==n&&(i[o]="none",ae.set(r,"display",n)));for(o=0;o<s;o++)null!=i[o]&&(e[o].style.display=i[o]);return e}S.fn.extend({show:function(){return xe(this,!0)},hide:function(){return xe(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ye(this)?S(this).show():S(this).hide()})}});var Oe,Ae,Se=/^(?:checkbox|radio)$/i,Ee=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Ce=/^$|^module$|\/(?:java|ecma)script/i;Oe=b.createDocumentFragment().appendChild(b.createElement("div")),(Ae=b.createElement("input")).setAttribute("type","radio"),Ae.setAttribute("checked","checked"),Ae.setAttribute("name","t"),Oe.appendChild(Ae),m.checkClone=Oe.cloneNode(!0).cloneNode(!0).lastChild.checked,Oe.innerHTML="<textarea>x</textarea>",m.noCloneChecked=!!Oe.cloneNode(!0).lastChild.defaultValue,Oe.innerHTML="<option></option>",m.option=!!Oe.lastChild;var Te={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function ke(e,t){var n;return n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&C(e,t)?S.merge([e],n):n}function Le(e,t){for(var n=0,r=e.length;n<r;n++)ae.set(e[n],"globalEval",!t||ae.get(t[n],"globalEval"))}Te.tbody=Te.tfoot=Te.colgroup=Te.caption=Te.thead,Te.th=Te.td,m.option||(Te.optgroup=Te.option=[1,"<select multiple='multiple'>","</select>"]);var je=/<|&#?\w+;/;function Ie(e,t,n,r,i){for(var o,s,a,l,c,u,d=t.createDocumentFragment(),p=[],f=0,h=e.length;f<h;f++)if((o=e[f])||0===o)if("object"===x(o))S.merge(p,o.nodeType?[o]:o);else if(je.test(o)){for(s=s||d.appendChild(t.createElement("div")),a=(Ee.exec(o)||["",""])[1].toLowerCase(),l=Te[a]||Te._default,s.innerHTML=l[1]+S.htmlPrefilter(o)+l[2],u=l[0];u--;)s=s.lastChild;S.merge(p,s.childNodes),(s=d.firstChild).textContent=""}else p.push(t.createTextNode(o));for(d.textContent="",f=0;o=p[f++];)if(r&&S.inArray(o,r)>-1)i&&i.push(o);else if(c=me(o),s=ke(d.appendChild(o),"script"),c&&Le(s),n)for(u=0;o=s[u++];)Ce.test(o.type||"")&&n.push(o);return d}var De=/^([^.]*)(?:\.(.+)|)/;function Pe(){return!0}function Ne(){return!1}function Fe(e,t,n,r,i,o){var s,a;if("object"==typeof t){for(a in"string"!=typeof n&&(r=r||n,n=void 0),t)Fe(e,a,n,r,t[a],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=Ne;else if(!i)return e;return 1===o&&(s=i,i=function(e){return S().off(e),s.apply(this,arguments)},i.guid=s.guid||(s.guid=S.guid++)),e.each(function(){S.event.add(this,t,i,r,n)})}function Me(e,t,n){n?(ae.set(e,t,!1),S.event.add(e,t,{namespace:!1,handler:function(e){var n,r=ae.get(this,t);if(1&e.isTrigger&&this[t]){if(r)(S.event.special[t]||{}).delegateType&&e.stopPropagation();else if(r=a.call(arguments),ae.set(this,t,r),this[t](),n=ae.get(this,t),ae.set(this,t,!1),r!==n)return e.stopImmediatePropagation(),e.preventDefault(),n}else r&&(ae.set(this,t,S.event.trigger(r[0],r.slice(1),this)),e.stopPropagation(),e.isImmediatePropagationStopped=Pe)}})):void 0===ae.get(e,t)&&S.event.add(e,t,Pe)}S.event={global:{},add:function(e,t,n,r,i){var o,s,a,l,c,u,d,p,f,h,g,m=ae.get(e);if(oe(e))for(n.handler&&(n=(o=n).handler,i=o.selector),i&&S.find.matchesSelector(ge,i),n.guid||(n.guid=S.guid++),(l=m.events)||(l=m.events=Object.create(null)),(s=m.handle)||(s=m.handle=function(t){return void 0!==S&&S.event.triggered!==t.type?S.event.dispatch.apply(e,arguments):void 0}),c=(t=(t||"").match(X)||[""]).length;c--;)f=g=(a=De.exec(t[c])||[])[1],h=(a[2]||"").split(".").sort(),f&&(d=S.event.special[f]||{},f=(i?d.delegateType:d.bindType)||f,d=S.event.special[f]||{},u=S.extend({type:f,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&S.expr.match.needsContext.test(i),namespace:h.join(".")},o),(p=l[f])||((p=l[f]=[]).delegateCount=0,d.setup&&!1!==d.setup.call(e,r,h,s)||e.addEventListener&&e.addEventListener(f,s)),d.add&&(d.add.call(e,u),u.handler.guid||(u.handler.guid=n.guid)),i?p.splice(p.delegateCount++,0,u):p.push(u),S.event.global[f]=!0)},remove:function(e,t,n,r,i){var o,s,a,l,c,u,d,p,f,h,g,m=ae.hasData(e)&&ae.get(e);if(m&&(l=m.events)){for(c=(t=(t||"").match(X)||[""]).length;c--;)if(f=g=(a=De.exec(t[c])||[])[1],h=(a[2]||"").split(".").sort(),f){for(d=S.event.special[f]||{},p=l[f=(r?d.delegateType:d.bindType)||f]||[],a=a[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=p.length;o--;)u=p[o],!i&&g!==u.origType||n&&n.guid!==u.guid||a&&!a.test(u.namespace)||r&&r!==u.selector&&("**"!==r||!u.selector)||(p.splice(o,1),u.selector&&p.delegateCount--,d.remove&&d.remove.call(e,u));s&&!p.length&&(d.teardown&&!1!==d.teardown.call(e,h,m.handle)||S.removeEvent(e,f,m.handle),delete l[f])}else for(f in l)S.event.remove(e,f+t[c],n,r,!0);S.isEmptyObject(l)&&ae.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,s,a=new Array(arguments.length),l=S.event.fix(e),c=(ae.get(this,"events")||Object.create(null))[l.type]||[],u=S.event.special[l.type]||{};for(a[0]=l,t=1;t<arguments.length;t++)a[t]=arguments[t];if(l.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,l)){for(s=S.event.handlers.call(this,l,c),t=0;(i=s[t++])&&!l.isPropagationStopped();)for(l.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==o.namespace&&!l.rnamespace.test(o.namespace)||(l.handleObj=o,l.data=o.data,void 0!==(r=((S.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,a))&&!1===(l.result=r)&&(l.preventDefault(),l.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,l),l.result}},handlers:function(e,t){var n,r,i,o,s,a=[],l=t.delegateCount,c=e.target;if(l&&c.nodeType&&!("click"===e.type&&e.button>=1))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==e.type||!0!==c.disabled)){for(o=[],s={},n=0;n<l;n++)void 0===s[i=(r=t[n]).selector+" "]&&(s[i]=r.needsContext?S(i,this).index(c)>-1:S.find(i,this,null,[c]).length),s[i]&&o.push(r);o.length&&a.push({elem:c,handlers:o})}return c=this,l<t.length&&a.push({elem:c,handlers:t.slice(l)}),a},addProp:function(e,t){Object.defineProperty(S.Event.prototype,e,{enumerable:!0,configurable:!0,get:v(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[S.expando]?e:new S.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return Se.test(t.type)&&t.click&&C(t,"input")&&Me(t,"click",!0),!1},trigger:function(e){var t=this||e;return Se.test(t.type)&&t.click&&C(t,"input")&&Me(t,"click"),!0},_default:function(e){var t=e.target;return Se.test(t.type)&&t.click&&C(t,"input")&&ae.get(t,"click")||C(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},S.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},S.Event=function(e,t){if(!(this instanceof S.Event))return new S.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Pe:Ne,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&S.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[S.expando]=!0},S.Event.prototype={constructor:S.Event,isDefaultPrevented:Ne,isPropagationStopped:Ne,isImmediatePropagationStopped:Ne,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Pe,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Pe,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Pe,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},S.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},S.event.addProp),S.each({focus:"focusin",blur:"focusout"},function(e,t){function n(e){if(b.documentMode){var n=ae.get(this,"handle"),r=S.event.fix(e);r.type="focusin"===e.type?"focus":"blur",r.isSimulated=!0,n(e),r.target===r.currentTarget&&n(r)}else S.event.simulate(t,e.target,S.event.fix(e))}S.event.special[e]={setup:function(){var r;if(Me(this,e,!0),!b.documentMode)return!1;(r=ae.get(this,t))||this.addEventListener(t,n),ae.set(this,t,(r||0)+1)},trigger:function(){return Me(this,e),!0},teardown:function(){var e;if(!b.documentMode)return!1;(e=ae.get(this,t)-1)?ae.set(this,t,e):(this.removeEventListener(t,n),ae.remove(this,t))},_default:function(t){return ae.get(t.target,e)},delegateType:t},S.event.special[t]={setup:function(){var r=this.ownerDocument||this.document||this,i=b.documentMode?this:r,o=ae.get(i,t);o||(b.documentMode?this.addEventListener(t,n):r.addEventListener(e,n,!0)),ae.set(i,t,(o||0)+1)},teardown:function(){var r=this.ownerDocument||this.document||this,i=b.documentMode?this:r,o=ae.get(i,t)-1;o?ae.set(i,t,o):(b.documentMode?this.removeEventListener(t,n):r.removeEventListener(e,n,!0),ae.remove(i,t))}}}),S.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){S.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=e.relatedTarget,i=e.handleObj;return r&&(r===this||S.contains(this,r))||(e.type=i.origType,n=i.handler.apply(this,arguments),e.type=t),n}}}),S.fn.extend({on:function(e,t,n,r){return Fe(this,e,t,n,r)},one:function(e,t,n,r){return Fe(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,S(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Ne),this.each(function(){S.event.remove(this,e,n,t)})}});var $e=/<script|<style|<link/i,qe=/checked\s*(?:[^=]|=\s*.checked.)/i,He=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Re(e,t){return C(e,"table")&&C(11!==t.nodeType?t:t.firstChild,"tr")&&S(e).children("tbody")[0]||e}function Be(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function We(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function ze(e,t){var n,r,i,o,s,a;if(1===t.nodeType){if(ae.hasData(e)&&(a=ae.get(e).events))for(i in ae.remove(t,"handle events"),a)for(n=0,r=a[i].length;n<r;n++)S.event.add(t,i,a[i][n]);le.hasData(e)&&(o=le.access(e),s=S.extend({},o),le.set(t,s))}}function Ve(e,t){var n=t.nodeName.toLowerCase();"input"===n&&Se.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function Ue(e,t,n,r){t=l(t);var i,o,s,a,c,u,d=0,p=e.length,f=p-1,h=t[0],g=v(h);if(g||p>1&&"string"==typeof h&&!m.checkClone&&qe.test(h))return e.each(function(i){var o=e.eq(i);g&&(t[0]=h.call(this,i,o.html())),Ue(o,t,n,r)});if(p&&(o=(i=Ie(t,e[0].ownerDocument,!1,e,r)).firstChild,1===i.childNodes.length&&(i=o),o||r)){for(a=(s=S.map(ke(i,"script"),Be)).length;d<p;d++)c=i,d!==f&&(c=S.clone(c,!0,!0),a&&S.merge(s,ke(c,"script"))),n.call(e[d],c,d);if(a)for(u=s[s.length-1].ownerDocument,S.map(s,We),d=0;d<a;d++)c=s[d],Ce.test(c.type||"")&&!ae.access(c,"globalEval")&&S.contains(u,c)&&(c.src&&"module"!==(c.type||"").toLowerCase()?S._evalUrl&&!c.noModule&&S._evalUrl(c.src,{nonce:c.nonce||c.getAttribute("nonce")},u):w(c.textContent.replace(He,""),c,u))}return e}function Xe(e,t,n){for(var r,i=t?S.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||S.cleanData(ke(r)),r.parentNode&&(n&&me(r)&&Le(ke(r,"script")),r.parentNode.removeChild(r));return e}S.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,s,a=e.cloneNode(!0),l=me(e);if(!(m.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||S.isXMLDoc(e)))for(s=ke(a),r=0,i=(o=ke(e)).length;r<i;r++)Ve(o[r],s[r]);if(t)if(n)for(o=o||ke(e),s=s||ke(a),r=0,i=o.length;r<i;r++)ze(o[r],s[r]);else ze(e,a);return(s=ke(a,"script")).length>0&&Le(s,!l&&ke(e,"script")),a},cleanData:function(e){for(var t,n,r,i=S.event.special,o=0;void 0!==(n=e[o]);o++)if(oe(n)){if(t=n[ae.expando]){if(t.events)for(r in t.events)i[r]?S.event.remove(n,r):S.removeEvent(n,r,t.handle);n[ae.expando]=void 0}n[le.expando]&&(n[le.expando]=void 0)}}}),S.fn.extend({detach:function(e){return Xe(this,e,!0)},remove:function(e){return Xe(this,e)},text:function(e){return ee(this,function(e){return void 0===e?S.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return Ue(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Re(this,e).appendChild(e)})},prepend:function(){return Ue(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Re(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return Ue(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Ue(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(S.cleanData(ke(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return S.clone(this,e,t)})},html:function(e){return ee(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!$e.test(e)&&!Te[(Ee.exec(e)||["",""])[1].toLowerCase()]){e=S.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(S.cleanData(ke(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=[];return Ue(this,arguments,function(t){var n=this.parentNode;S.inArray(this,e)<0&&(S.cleanData(ke(this)),n&&n.replaceChild(t,this))},e)}}),S.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){S.fn[e]=function(e){for(var n,r=[],i=S(e),o=i.length-1,s=0;s<=o;s++)n=s===o?this:this.clone(!0),S(i[s])[t](n),c.apply(r,n.get());return this.pushStack(r)}});var Ke=new RegExp("^("+pe+")(?!px)[a-z%]+$","i"),Ge=/^--/,Qe=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=r),t.getComputedStyle(e)},Ye=function(e,t,n){var r,i,o={};for(i in t)o[i]=e.style[i],e.style[i]=t[i];for(i in r=n.call(e),t)e.style[i]=o[i];return r},Je=new RegExp(he.join("|"),"i");function Ze(e,t,n){var r,i,o,s,a=Ge.test(t),l=e.style;return(n=n||Qe(e))&&(s=n.getPropertyValue(t)||n[t],a&&s&&(s=s.replace(I,"$1")||void 0),""!==s||me(e)||(s=S.style(e,t)),!m.pixelBoxStyles()&&Ke.test(s)&&Je.test(t)&&(r=l.width,i=l.minWidth,o=l.maxWidth,l.minWidth=l.maxWidth=l.width=s,s=n.width,l.width=r,l.minWidth=i,l.maxWidth=o)),void 0!==s?s+"":s}function et(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(u){c.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",u.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",ge.appendChild(c).appendChild(u);var e=r.getComputedStyle(u);n="1%"!==e.top,l=12===t(e.marginLeft),u.style.right="60%",s=36===t(e.right),i=36===t(e.width),u.style.position="absolute",o=12===t(u.offsetWidth/3),ge.removeChild(c),u=null}}function t(e){return Math.round(parseFloat(e))}var n,i,o,s,a,l,c=b.createElement("div"),u=b.createElement("div");u.style&&(u.style.backgroundClip="content-box",u.cloneNode(!0).style.backgroundClip="",m.clearCloneStyle="content-box"===u.style.backgroundClip,S.extend(m,{boxSizingReliable:function(){return e(),i},pixelBoxStyles:function(){return e(),s},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),o},reliableTrDimensions:function(){var e,t,n,i;return null==a&&(e=b.createElement("table"),t=b.createElement("tr"),n=b.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="box-sizing:content-box;border:1px solid",t.style.height="1px",n.style.height="9px",n.style.display="block",ge.appendChild(e).appendChild(t).appendChild(n),i=r.getComputedStyle(t),a=parseInt(i.height,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10)===t.offsetHeight,ge.removeChild(e)),a}}))}();var tt=["Webkit","Moz","ms"],nt=b.createElement("div").style,rt={};function it(e){var t=S.cssProps[e]||rt[e];return t||(e in nt?e:rt[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=tt.length;n--;)if((e=tt[n]+t)in nt)return e}(e)||e)}var ot=/^(none|table(?!-c[ea]).+)/,st={position:"absolute",visibility:"hidden",display:"block"},at={letterSpacing:"0",fontWeight:"400"};function lt(e,t,n){var r=fe.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function ct(e,t,n,r,i,o){var s="width"===t?1:0,a=0,l=0,c=0;if(n===(r?"border":"content"))return 0;for(;s<4;s+=2)"margin"===n&&(c+=S.css(e,n+he[s],!0,i)),r?("content"===n&&(l-=S.css(e,"padding"+he[s],!0,i)),"margin"!==n&&(l-=S.css(e,"border"+he[s]+"Width",!0,i))):(l+=S.css(e,"padding"+he[s],!0,i),"padding"!==n?l+=S.css(e,"border"+he[s]+"Width",!0,i):a+=S.css(e,"border"+he[s]+"Width",!0,i));return!r&&o>=0&&(l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-l-a-.5))||0),l+c}function ut(e,t,n){var r=Qe(e),i=(!m.boxSizingReliable()||n)&&"border-box"===S.css(e,"boxSizing",!1,r),o=i,s=Ze(e,t,r),a="offset"+t[0].toUpperCase()+t.slice(1);if(Ke.test(s)){if(!n)return s;s="auto"}return(!m.boxSizingReliable()&&i||!m.reliableTrDimensions()&&C(e,"tr")||"auto"===s||!parseFloat(s)&&"inline"===S.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===S.css(e,"boxSizing",!1,r),(o=a in e)&&(s=e[a])),(s=parseFloat(s)||0)+ct(e,t,n||(i?"border":"content"),o,r,s)+"px"}function dt(e,t,n,r,i){return new dt.prototype.init(e,t,n,r,i)}S.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Ze(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,s,a=ie(t),l=Ge.test(t),c=e.style;if(l||(t=it(a)),s=S.cssHooks[t]||S.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(i=s.get(e,!1,r))?i:c[t];"string"===(o=typeof n)&&(i=fe.exec(n))&&i[1]&&(n=be(e,t,i),o="number"),null!=n&&n==n&&("number"!==o||l||(n+=i&&i[3]||(S.cssNumber[a]?"":"px")),m.clearCloneStyle||""!==n||0!==t.indexOf("background")||(c[t]="inherit"),s&&"set"in s&&void 0===(n=s.set(e,n,r))||(l?c.setProperty(t,n):c[t]=n))}},css:function(e,t,n,r){var i,o,s,a=ie(t);return Ge.test(t)||(t=it(a)),(s=S.cssHooks[t]||S.cssHooks[a])&&"get"in s&&(i=s.get(e,!0,n)),void 0===i&&(i=Ze(e,t,r)),"normal"===i&&t in at&&(i=at[t]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),S.each(["height","width"],function(e,t){S.cssHooks[t]={get:function(e,n,r){if(n)return!ot.test(S.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?ut(e,t,r):Ye(e,st,function(){return ut(e,t,r)})},set:function(e,n,r){var i,o=Qe(e),s=!m.scrollboxSize()&&"absolute"===o.position,a=(s||r)&&"border-box"===S.css(e,"boxSizing",!1,o),l=r?ct(e,t,r,a,o):0;return a&&s&&(l-=Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(o[t])-ct(e,t,"border",!1,o)-.5)),l&&(i=fe.exec(n))&&"px"!==(i[3]||"px")&&(e.style[t]=n,n=S.css(e,t)),lt(0,n,l)}}}),S.cssHooks.marginLeft=et(m.reliableMarginLeft,function(e,t){if(t)return(parseFloat(Ze(e,"marginLeft"))||e.getBoundingClientRect().left-Ye(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),S.each({margin:"",padding:"",border:"Width"},function(e,t){S.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[e+he[r]+t]=o[r]||o[r-2]||o[0];return i}},"margin"!==e&&(S.cssHooks[e+t].set=lt)}),S.fn.extend({css:function(e,t){return ee(this,function(e,t,n){var r,i,o={},s=0;if(Array.isArray(t)){for(r=Qe(e),i=t.length;s<i;s++)o[t[s]]=S.css(e,t[s],!1,r);return o}return void 0!==n?S.style(e,t,n):S.css(e,t)},e,t,arguments.length>1)}}),S.Tween=dt,dt.prototype={constructor:dt,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||S.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(S.cssNumber[n]?"":"px")},cur:function(){var e=dt.propHooks[this.prop];return e&&e.get?e.get(this):dt.propHooks._default.get(this)},run:function(e){var t,n=dt.propHooks[this.prop];return this.options.duration?this.pos=t=S.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):dt.propHooks._default.set(this),this}},dt.prototype.init.prototype=dt.prototype,dt.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=S.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){S.fx.step[e.prop]?S.fx.step[e.prop](e):1!==e.elem.nodeType||!S.cssHooks[e.prop]&&null==e.elem.style[it(e.prop)]?e.elem[e.prop]=e.now:S.style(e.elem,e.prop,e.now+e.unit)}}},dt.propHooks.scrollTop=dt.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},S.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},S.fx=dt.prototype.init,S.fx.step={};var pt,ft,ht=/^(?:toggle|show|hide)$/,gt=/queueHooks$/;function mt(){ft&&(!1===b.hidden&&r.requestAnimationFrame?r.requestAnimationFrame(mt):r.setTimeout(mt,S.fx.interval),S.fx.tick())}function vt(){return r.setTimeout(function(){pt=void 0}),pt=Date.now()}function yt(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=he[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function bt(e,t,n){for(var r,i=(_t.tweeners[t]||[]).concat(_t.tweeners["*"]),o=0,s=i.length;o<s;o++)if(r=i[o].call(n,t,e))return r}function _t(e,t,n){var r,i,o=0,s=_t.prefilters.length,a=S.Deferred().always(function(){delete l.elem}),l=function(){if(i)return!1;for(var t=pt||vt(),n=Math.max(0,c.startTime+c.duration-t),r=1-(n/c.duration||0),o=0,s=c.tweens.length;o<s;o++)c.tweens[o].run(r);return a.notifyWith(e,[c,r,n]),r<1&&s?n:(s||a.notifyWith(e,[c,1,0]),a.resolveWith(e,[c]),!1)},c=a.promise({elem:e,props:S.extend({},t),opts:S.extend(!0,{specialEasing:{},easing:S.easing._default},n),originalProperties:t,originalOptions:n,startTime:pt||vt(),duration:n.duration,tweens:[],createTween:function(t,n){var r=S.Tween(e,c.opts,t,n,c.opts.specialEasing[t]||c.opts.easing);return c.tweens.push(r),r},stop:function(t){var n=0,r=t?c.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)c.tweens[n].run(1);return t?(a.notifyWith(e,[c,1,0]),a.resolveWith(e,[c,t])):a.rejectWith(e,[c,t]),this}}),u=c.props;for(!function(e,t){var n,r,i,o,s;for(n in e)if(i=t[r=ie(n)],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(s=S.cssHooks[r])&&"expand"in s)for(n in o=s.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(u,c.opts.specialEasing);o<s;o++)if(r=_t.prefilters[o].call(c,e,u,c.opts))return v(r.stop)&&(S._queueHooks(c.elem,c.opts.queue).stop=r.stop.bind(r)),r;return S.map(u,bt,c),v(c.opts.start)&&c.opts.start.call(e,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),S.fx.timer(S.extend(l,{elem:e,anim:c,queue:c.opts.queue})),c}S.Animation=S.extend(_t,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return be(n.elem,e,fe.exec(t),n),n}]},tweener:function(e,t){v(e)?(t=e,e=["*"]):e=e.match(X);for(var n,r=0,i=e.length;r<i;r++)n=e[r],_t.tweeners[n]=_t.tweeners[n]||[],_t.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,i,o,s,a,l,c,u,d="width"in t||"height"in t,p=this,f={},h=e.style,g=e.nodeType&&ye(e),m=ae.get(e,"fxshow");for(r in n.queue||(null==(s=S._queueHooks(e,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,p.always(function(){p.always(function(){s.unqueued--,S.queue(e,"fx").length||s.empty.fire()})})),t)if(i=t[r],ht.test(i)){if(delete t[r],o=o||"toggle"===i,i===(g?"hide":"show")){if("show"!==i||!m||void 0===m[r])continue;g=!0}f[r]=m&&m[r]||S.style(e,r)}if((l=!S.isEmptyObject(t))||!S.isEmptyObject(f))for(r in d&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(c=m&&m.display)&&(c=ae.get(e,"display")),"none"===(u=S.css(e,"display"))&&(c?u=c:(xe([e],!0),c=e.style.display||c,u=S.css(e,"display"),xe([e]))),("inline"===u||"inline-block"===u&&null!=c)&&"none"===S.css(e,"float")&&(l||(p.done(function(){h.display=c}),null==c&&(u=h.display,c="none"===u?"":u)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",p.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),l=!1,f)l||(m?"hidden"in m&&(g=m.hidden):m=ae.access(e,"fxshow",{display:c}),o&&(m.hidden=!g),g&&xe([e],!0),p.done(function(){for(r in g||xe([e]),ae.remove(e,"fxshow"),f)S.style(e,r,f[r])})),l=bt(g?m[r]:0,r,p),r in m||(m[r]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?_t.prefilters.unshift(e):_t.prefilters.push(e)}}),S.speed=function(e,t,n){var r=e&&"object"==typeof e?S.extend({},e):{complete:n||!n&&t||v(e)&&e,duration:e,easing:n&&t||t&&!v(t)&&t};return S.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in S.fx.speeds?r.duration=S.fx.speeds[r.duration]:r.duration=S.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){v(r.old)&&r.old.call(this),r.queue&&S.dequeue(this,r.queue)},r},S.fn.extend({fadeTo:function(e,t,n,r){return this.filter(ye).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=S.isEmptyObject(e),o=S.speed(t,n,r),s=function(){var t=_t(this,S.extend({},e),o);(i||ae.get(this,"finish"))&&t.stop(!0)};return s.finish=s,i||!1===o.queue?this.each(s):this.queue(o.queue,s)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&this.queue(e||"fx",[]),this.each(function(){var t=!0,i=null!=e&&e+"queueHooks",o=S.timers,s=ae.get(this);if(i)s[i]&&s[i].stop&&r(s[i]);else for(i in s)s[i]&&s[i].stop&&gt.test(i)&&r(s[i]);for(i=o.length;i--;)o[i].elem!==this||null!=e&&o[i].queue!==e||(o[i].anim.stop(n),t=!1,o.splice(i,1));!t&&n||S.dequeue(this,e)})},finish:function(e){return!1!==e&&(e=e||"fx"),this.each(function(){var t,n=ae.get(this),r=n[e+"queue"],i=n[e+"queueHooks"],o=S.timers,s=r?r.length:0;for(n.finish=!0,S.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<s;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish})}}),S.each(["toggle","show","hide"],function(e,t){var n=S.fn[t];S.fn[t]=function(e,r,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(yt(t,!0),e,r,i)}}),S.each({slideDown:yt("show"),slideUp:yt("hide"),slideToggle:yt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){S.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}}),S.timers=[],S.fx.tick=function(){var e,t=0,n=S.timers;for(pt=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||S.fx.stop(),pt=void 0},S.fx.timer=function(e){S.timers.push(e),S.fx.start()},S.fx.interval=13,S.fx.start=function(){ft||(ft=!0,mt())},S.fx.stop=function(){ft=null},S.fx.speeds={slow:600,fast:200,_default:400},S.fn.delay=function(e,t){return e=S.fx&&S.fx.speeds[e]||e,t=t||"fx",this.queue(t,function(t,n){var i=r.setTimeout(t,e);n.stop=function(){r.clearTimeout(i)}})},function(){var e=b.createElement("input"),t=b.createElement("select").appendChild(b.createElement("option"));e.type="checkbox",m.checkOn=""!==e.value,m.optSelected=t.selected,(e=b.createElement("input")).value="t",e.type="radio",m.radioValue="t"===e.value}();var wt,xt=S.expr.attrHandle;S.fn.extend({attr:function(e,t){return ee(this,S.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){S.removeAttr(this,e)})}}),S.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?S.prop(e,t,n):(1===o&&S.isXMLDoc(e)||(i=S.attrHooks[t.toLowerCase()]||(S.expr.match.bool.test(t)?wt:void 0)),void 0!==n?null===n?void S.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):i&&"get"in i&&null!==(r=i.get(e,t))?r:null==(r=S.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){if(!m.radioValue&&"radio"===t&&C(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(X);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),wt={set:function(e,t,n){return!1===t?S.removeAttr(e,n):e.setAttribute(n,n),n}},S.each(S.expr.match.bool.source.match(/\w+/g),function(e,t){var n=xt[t]||S.find.attr;xt[t]=function(e,t,r){var i,o,s=t.toLowerCase();return r||(o=xt[s],xt[s]=i,i=null!=n(e,t,r)?s:null,xt[s]=o),i}});var Ot=/^(?:input|select|textarea|button)$/i,At=/^(?:a|area)$/i;function St(e){return(e.match(X)||[]).join(" ")}function Et(e){return e.getAttribute&&e.getAttribute("class")||""}function Ct(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(X)||[]}S.fn.extend({prop:function(e,t){return ee(this,S.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[S.propFix[e]||e]})}}),S.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&S.isXMLDoc(e)||(t=S.propFix[t]||t,i=S.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=S.find.attr(e,"tabindex");return t?parseInt(t,10):Ot.test(e.nodeName)||At.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),m.optSelected||(S.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),S.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){S.propFix[this.toLowerCase()]=this}),S.fn.extend({addClass:function(e){var t,n,r,i,o,s;return v(e)?this.each(function(t){S(this).addClass(e.call(this,t,Et(this)))}):(t=Ct(e)).length?this.each(function(){if(r=Et(this),n=1===this.nodeType&&" "+St(r)+" "){for(o=0;o<t.length;o++)i=t[o],n.indexOf(" "+i+" ")<0&&(n+=i+" ");s=St(n),r!==s&&this.setAttribute("class",s)}}):this},removeClass:function(e){var t,n,r,i,o,s;return v(e)?this.each(function(t){S(this).removeClass(e.call(this,t,Et(this)))}):arguments.length?(t=Ct(e)).length?this.each(function(){if(r=Et(this),n=1===this.nodeType&&" "+St(r)+" "){for(o=0;o<t.length;o++)for(i=t[o];n.indexOf(" "+i+" ")>-1;)n=n.replace(" "+i+" "," ");s=St(n),r!==s&&this.setAttribute("class",s)}}):this:this.attr("class","")},toggleClass:function(e,t){var n,r,i,o,s=typeof e,a="string"===s||Array.isArray(e);return v(e)?this.each(function(n){S(this).toggleClass(e.call(this,n,Et(this),t),t)}):"boolean"==typeof t&&a?t?this.addClass(e):this.removeClass(e):(n=Ct(e),this.each(function(){if(a)for(o=S(this),i=0;i<n.length;i++)r=n[i],o.hasClass(r)?o.removeClass(r):o.addClass(r);else void 0!==e&&"boolean"!==s||((r=Et(this))&&ae.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||!1===e?"":ae.get(this,"__className__")||""))}))},hasClass:function(e){var t,n,r=0;for(t=" "+e+" ";n=this[r++];)if(1===n.nodeType&&(" "+St(Et(n))+" ").indexOf(t)>-1)return!0;return!1}});var Tt=/\r/g;S.fn.extend({val:function(e){var t,n,r,i=this[0];return arguments.length?(r=v(e),this.each(function(n){var i;1===this.nodeType&&(null==(i=r?e.call(this,n,S(this).val()):e)?i="":"number"==typeof i?i+="":Array.isArray(i)&&(i=S.map(i,function(e){return null==e?"":e+""})),(t=S.valHooks[this.type]||S.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,i,"value")||(this.value=i))})):i?(t=S.valHooks[i.type]||S.valHooks[i.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(i,"value"))?n:"string"==typeof(n=i.value)?n.replace(Tt,""):null==n?"":n:void 0}}),S.extend({valHooks:{option:{get:function(e){var t=S.find.attr(e,"value");return null!=t?t:St(S.text(e))}},select:{get:function(e){var t,n,r,i=e.options,o=e.selectedIndex,s="select-one"===e.type,a=s?null:[],l=s?o+1:i.length;for(r=o<0?l:s?o:0;r<l;r++)if(((n=i[r]).selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!C(n.parentNode,"optgroup"))){if(t=S(n).val(),s)return t;a.push(t)}return a},set:function(e,t){for(var n,r,i=e.options,o=S.makeArray(t),s=i.length;s--;)((r=i[s]).selected=S.inArray(S.valHooks.option.get(r),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),S.each(["radio","checkbox"],function(){S.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=S.inArray(S(e).val(),t)>-1}},m.checkOn||(S.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var kt=r.location,Lt={guid:Date.now()},jt=/\?/;S.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{t=(new r.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=t&&t.getElementsByTagName("parsererror")[0],t&&!n||S.error("Invalid XML: "+(n?S.map(n.childNodes,function(e){return e.textContent}).join("\n"):e)),t};var It=/^(?:focusinfocus|focusoutblur)$/,Dt=function(e){e.stopPropagation()};S.extend(S.event,{trigger:function(e,t,n,i){var o,s,a,l,c,u,d,p,h=[n||b],g=f.call(e,"type")?e.type:e,m=f.call(e,"namespace")?e.namespace.split("."):[];if(s=p=a=n=n||b,3!==n.nodeType&&8!==n.nodeType&&!It.test(g+S.event.triggered)&&(g.indexOf(".")>-1&&(m=g.split("."),g=m.shift(),m.sort()),c=g.indexOf(":")<0&&"on"+g,(e=e[S.expando]?e:new S.Event(g,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=m.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:S.makeArray(t,[e]),d=S.event.special[g]||{},i||!d.trigger||!1!==d.trigger.apply(n,t))){if(!i&&!d.noBubble&&!y(n)){for(l=d.delegateType||g,It.test(l+g)||(s=s.parentNode);s;s=s.parentNode)h.push(s),a=s;a===(n.ownerDocument||b)&&h.push(a.defaultView||a.parentWindow||r)}for(o=0;(s=h[o++])&&!e.isPropagationStopped();)p=s,e.type=o>1?l:d.bindType||g,(u=(ae.get(s,"events")||Object.create(null))[e.type]&&ae.get(s,"handle"))&&u.apply(s,t),(u=c&&s[c])&&u.apply&&oe(s)&&(e.result=u.apply(s,t),!1===e.result&&e.preventDefault());return e.type=g,i||e.isDefaultPrevented()||d._default&&!1!==d._default.apply(h.pop(),t)||!oe(n)||c&&v(n[g])&&!y(n)&&((a=n[c])&&(n[c]=null),S.event.triggered=g,e.isPropagationStopped()&&p.addEventListener(g,Dt),n[g](),e.isPropagationStopped()&&p.removeEventListener(g,Dt),S.event.triggered=void 0,a&&(n[c]=a)),e.result}},simulate:function(e,t,n){var r=S.extend(new S.Event,n,{type:e,isSimulated:!0});S.event.trigger(r,null,t)}}),S.fn.extend({trigger:function(e,t){return this.each(function(){S.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return S.event.trigger(e,t,n,!0)}});var Pt=/\[\]$/,Nt=/\r?\n/g,Ft=/^(?:submit|button|image|reset|file)$/i,Mt=/^(?:input|select|textarea|keygen)/i;function $t(e,t,n,r){var i;if(Array.isArray(t))S.each(t,function(t,i){n||Pt.test(e)?r(e,i):$t(e+"["+("object"==typeof i&&null!=i?t:"")+"]",i,n,r)});else if(n||"object"!==x(t))r(e,t);else for(i in t)$t(e+"["+i+"]",t[i],n,r)}S.param=function(e,t){var n,r=[],i=function(e,t){var n=v(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!S.isPlainObject(e))S.each(e,function(){i(this.name,this.value)});else for(n in e)$t(n,e[n],t,i);return r.join("&")},S.fn.extend({serialize:function(){return S.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=S.prop(this,"elements");return e?S.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!S(this).is(":disabled")&&Mt.test(this.nodeName)&&!Ft.test(e)&&(this.checked||!Se.test(e))}).map(function(e,t){var n=S(this).val();return null==n?null:Array.isArray(n)?S.map(n,function(e){return{name:t.name,value:e.replace(Nt,"\r\n")}}):{name:t.name,value:n.replace(Nt,"\r\n")}}).get()}});var qt=/%20/g,Ht=/#.*$/,Rt=/([?&])_=[^&]*/,Bt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Wt=/^(?:GET|HEAD)$/,zt=/^\/\//,Vt={},Ut={},Xt="*/".concat("*"),Kt=b.createElement("a");function Gt(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,i=0,o=t.toLowerCase().match(X)||[];if(v(n))for(;r=o[i++];)"+"===r[0]?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function Qt(e,t,n,r){var i={},o=e===Ut;function s(a){var l;return i[a]=!0,S.each(e[a]||[],function(e,a){var c=a(t,n,r);return"string"!=typeof c||o||i[c]?o?!(l=c):void 0:(t.dataTypes.unshift(c),s(c),!1)}),l}return s(t.dataTypes[0])||!i["*"]&&s("*")}function Yt(e,t){var n,r,i=S.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&S.extend(!0,e,r),e}Kt.href=kt.href,S.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:kt.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(kt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Xt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":S.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Yt(Yt(e,S.ajaxSettings),t):Yt(S.ajaxSettings,e)},ajaxPrefilter:Gt(Vt),ajaxTransport:Gt(Ut),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var n,i,o,s,a,l,c,u,d,p,f=S.ajaxSetup({},t),h=f.context||f,g=f.context&&(h.nodeType||h.jquery)?S(h):S.event,m=S.Deferred(),v=S.Callbacks("once memory"),y=f.statusCode||{},_={},w={},x="canceled",O={readyState:0,getResponseHeader:function(e){var t;if(c){if(!s)for(s={};t=Bt.exec(o);)s[t[1].toLowerCase()+" "]=(s[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=s[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return c?o:null},setRequestHeader:function(e,t){return null==c&&(e=w[e.toLowerCase()]=w[e.toLowerCase()]||e,_[e]=t),this},overrideMimeType:function(e){return null==c&&(f.mimeType=e),this},statusCode:function(e){var t;if(e)if(c)O.always(e[O.status]);else for(t in e)y[t]=[y[t],e[t]];return this},abort:function(e){var t=e||x;return n&&n.abort(t),A(0,t),this}};if(m.promise(O),f.url=((e||f.url||kt.href)+"").replace(zt,kt.protocol+"//"),f.type=t.method||t.type||f.method||f.type,f.dataTypes=(f.dataType||"*").toLowerCase().match(X)||[""],null==f.crossDomain){l=b.createElement("a");try{l.href=f.url,l.href=l.href,f.crossDomain=Kt.protocol+"//"+Kt.host!=l.protocol+"//"+l.host}catch(e){f.crossDomain=!0}}if(f.data&&f.processData&&"string"!=typeof f.data&&(f.data=S.param(f.data,f.traditional)),Qt(Vt,f,t,O),c)return O;for(d in(u=S.event&&f.global)&&0===S.active++&&S.event.trigger("ajaxStart"),f.type=f.type.toUpperCase(),f.hasContent=!Wt.test(f.type),i=f.url.replace(Ht,""),f.hasContent?f.data&&f.processData&&0===(f.contentType||"").indexOf("application/x-www-form-urlencoded")&&(f.data=f.data.replace(qt,"+")):(p=f.url.slice(i.length),f.data&&(f.processData||"string"==typeof f.data)&&(i+=(jt.test(i)?"&":"?")+f.data,delete f.data),!1===f.cache&&(i=i.replace(Rt,"$1"),p=(jt.test(i)?"&":"?")+"_="+Lt.guid+++p),f.url=i+p),f.ifModified&&(S.lastModified[i]&&O.setRequestHeader("If-Modified-Since",S.lastModified[i]),S.etag[i]&&O.setRequestHeader("If-None-Match",S.etag[i])),(f.data&&f.hasContent&&!1!==f.contentType||t.contentType)&&O.setRequestHeader("Content-Type",f.contentType),O.setRequestHeader("Accept",f.dataTypes[0]&&f.accepts[f.dataTypes[0]]?f.accepts[f.dataTypes[0]]+("*"!==f.dataTypes[0]?", "+Xt+"; q=0.01":""):f.accepts["*"]),f.headers)O.setRequestHeader(d,f.headers[d]);if(f.beforeSend&&(!1===f.beforeSend.call(h,O,f)||c))return O.abort();if(x="abort",v.add(f.complete),O.done(f.success),O.fail(f.error),n=Qt(Ut,f,t,O)){if(O.readyState=1,u&&g.trigger("ajaxSend",[O,f]),c)return O;f.async&&f.timeout>0&&(a=r.setTimeout(function(){O.abort("timeout")},f.timeout));try{c=!1,n.send(_,A)}catch(e){if(c)throw e;A(-1,e)}}else A(-1,"No Transport");function A(e,t,s,l){var d,p,b,_,w,x=t;c||(c=!0,a&&r.clearTimeout(a),n=void 0,o=l||"",O.readyState=e>0?4:0,d=e>=200&&e<300||304===e,s&&(_=function(e,t,n){for(var r,i,o,s,a=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in a)if(a[i]&&a[i].test(r)){l.unshift(i);break}if(l[0]in n)o=l[0];else{for(i in n){if(!l[0]||e.converters[i+" "+l[0]]){o=i;break}s||(s=i)}o=o||s}if(o)return o!==l[0]&&l.unshift(o),n[o]}(f,O,s)),!d&&S.inArray("script",f.dataTypes)>-1&&S.inArray("json",f.dataTypes)<0&&(f.converters["text script"]=function(){}),_=function(e,t,n,r){var i,o,s,a,l,c={},u=e.dataTypes.slice();if(u[1])for(s in e.converters)c[s.toLowerCase()]=e.converters[s];for(o=u.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=u.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(s=c[l+" "+o]||c["* "+o]))for(i in c)if((a=i.split(" "))[1]===o&&(s=c[l+" "+a[0]]||c["* "+a[0]])){!0===s?s=c[i]:!0!==c[i]&&(o=a[0],u.unshift(a[1]));break}if(!0!==s)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}(f,_,O,d),d?(f.ifModified&&((w=O.getResponseHeader("Last-Modified"))&&(S.lastModified[i]=w),(w=O.getResponseHeader("etag"))&&(S.etag[i]=w)),204===e||"HEAD"===f.type?x="nocontent":304===e?x="notmodified":(x=_.state,p=_.data,d=!(b=_.error))):(b=x,!e&&x||(x="error",e<0&&(e=0))),O.status=e,O.statusText=(t||x)+"",d?m.resolveWith(h,[p,x,O]):m.rejectWith(h,[O,x,b]),O.statusCode(y),y=void 0,u&&g.trigger(d?"ajaxSuccess":"ajaxError",[O,f,d?p:b]),v.fireWith(h,[O,x]),u&&(g.trigger("ajaxComplete",[O,f]),--S.active||S.event.trigger("ajaxStop")))}return O},getJSON:function(e,t,n){return S.get(e,t,n,"json")},getScript:function(e,t){return S.get(e,void 0,t,"script")}}),S.each(["get","post"],function(e,t){S[t]=function(e,n,r,i){return v(n)&&(i=i||r,r=n,n=void 0),S.ajax(S.extend({url:e,type:t,dataType:i,data:n,success:r},S.isPlainObject(e)&&e))}}),S.ajaxPrefilter(function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),S._evalUrl=function(e,t,n){return S.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){S.globalEval(e,t,n)}})},S.fn.extend({wrapAll:function(e){var t;return this[0]&&(v(e)&&(e=e.call(this[0])),t=S(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(e){return v(e)?this.each(function(t){S(this).wrapInner(e.call(this,t))}):this.each(function(){var t=S(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=v(e);return this.each(function(n){S(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){S(this).replaceWith(this.childNodes)}),this}}),S.expr.pseudos.hidden=function(e){return!S.expr.pseudos.visible(e)},S.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},S.ajaxSettings.xhr=function(){try{return new r.XMLHttpRequest}catch(e){}};var Jt={0:200,1223:204},Zt=S.ajaxSettings.xhr();m.cors=!!Zt&&"withCredentials"in Zt,m.ajax=Zt=!!Zt,S.ajaxTransport(function(e){var t,n;if(m.cors||Zt&&!e.crossDomain)return{send:function(i,o){var s,a=e.xhr();if(a.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(s in e.xhrFields)a[s]=e.xhrFields[s];for(s in e.mimeType&&a.overrideMimeType&&a.overrideMimeType(e.mimeType),e.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),i)a.setRequestHeader(s,i[s]);t=function(e){return function(){t&&(t=n=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,"abort"===e?a.abort():"error"===e?"number"!=typeof a.status?o(0,"error"):o(a.status,a.statusText):o(Jt[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=t(),n=a.onerror=a.ontimeout=t("error"),void 0!==a.onabort?a.onabort=n:a.onreadystatechange=function(){4===a.readyState&&r.setTimeout(function(){t&&n()})},t=t("abort");try{a.send(e.hasContent&&e.data||null)}catch(e){if(t)throw e}},abort:function(){t&&t()}}}),S.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),S.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return S.globalEval(e),e}}}),S.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),S.ajaxTransport("script",function(e){var t,n;if(e.crossDomain||e.scriptAttrs)return{send:function(r,i){t=S("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&i("error"===e.type?404:200,e.type)}),b.head.appendChild(t[0])},abort:function(){n&&n()}}});var en,tn=[],nn=/(=)\?(?=&|$)|\?\?/;S.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=tn.pop()||S.expando+"_"+Lt.guid++;return this[e]=!0,e}}),S.ajaxPrefilter("json jsonp",function(e,t,n){var i,o,s,a=!1!==e.jsonp&&(nn.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&nn.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=v(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(nn,"$1"+i):!1!==e.jsonp&&(e.url+=(jt.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return s||S.error(i+" was not called"),s[0]},e.dataTypes[0]="json",o=r[i],r[i]=function(){s=arguments},n.always(function(){void 0===o?S(r).removeProp(i):r[i]=o,e[i]&&(e.jsonpCallback=t.jsonpCallback,tn.push(i)),s&&v(o)&&o(s[0]),s=o=void 0}),"script"}),m.createHTMLDocument=((en=b.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===en.childNodes.length),S.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(m.createHTMLDocument?((r=(t=b.implementation.createHTMLDocument("")).createElement("base")).href=b.location.href,t.head.appendChild(r)):t=b),o=!n&&[],(i=H.exec(e))?[t.createElement(i[1])]:(i=Ie([e],t,o),o&&o.length&&S(o).remove(),S.merge([],i.childNodes)));var r,i,o},S.fn.load=function(e,t,n){var r,i,o,s=this,a=e.indexOf(" ");return a>-1&&(r=St(e.slice(a)),e=e.slice(0,a)),v(t)?(n=t,t=void 0):t&&"object"==typeof t&&(i="POST"),s.length>0&&S.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done(function(e){o=arguments,s.html(r?S("<div>").append(S.parseHTML(e)).find(r):e)}).always(n&&function(e,t){s.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},S.expr.pseudos.animated=function(e){return S.grep(S.timers,function(t){return e===t.elem}).length},S.offset={setOffset:function(e,t,n){var r,i,o,s,a,l,c=S.css(e,"position"),u=S(e),d={};"static"===c&&(e.style.position="relative"),a=u.offset(),o=S.css(e,"top"),l=S.css(e,"left"),("absolute"===c||"fixed"===c)&&(o+l).indexOf("auto")>-1?(s=(r=u.position()).top,i=r.left):(s=parseFloat(o)||0,i=parseFloat(l)||0),v(t)&&(t=t.call(e,n,S.extend({},a))),null!=t.top&&(d.top=t.top-a.top+s),null!=t.left&&(d.left=t.left-a.left+i),"using"in t?t.using.call(e,d):u.css(d)}},S.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){S.offset.setOffset(this,e,t)});var t,n,r=this[0];return r?r.getClientRects().length?(t=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===S.css(r,"position"))t=r.getBoundingClientRect();else{for(t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===S.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=S(e).offset()).top+=S.css(e,"borderTopWidth",!0),i.left+=S.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-S.css(r,"marginTop",!0),left:t.left-i.left-S.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===S.css(e,"position");)e=e.offsetParent;return e||ge})}}),S.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n="pageYOffset"===t;S.fn[e]=function(r){return ee(this,function(e,r,i){var o;if(y(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===i)return o?o[t]:e[r];o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):e[r]=i},e,r,arguments.length)}}),S.each(["top","left"],function(e,t){S.cssHooks[t]=et(m.pixelPosition,function(e,n){if(n)return n=Ze(e,t),Ke.test(n)?S(e).position()[t]+"px":n})}),S.each({Height:"height",Width:"width"},function(e,t){S.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,r){S.fn[r]=function(i,o){var s=arguments.length&&(n||"boolean"!=typeof i),a=n||(!0===i||!0===o?"margin":"border");return ee(this,function(t,n,i){var o;return y(t)?0===r.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===i?S.css(t,n,a):S.style(t,n,i,a)},t,s?i:void 0,s)}})}),S.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){S.fn[t]=function(e){return this.on(t,e)}}),S.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}}),S.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){S.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}});var rn=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;S.proxy=function(e,t){var n,r,i;if("string"==typeof t&&(n=e[t],t=e,e=n),v(e))return r=a.call(arguments,2),i=function(){return e.apply(t||this,r.concat(a.call(arguments)))},i.guid=e.guid=e.guid||S.guid++,i},S.holdReady=function(e){e?S.readyWait++:S.ready(!0)},S.isArray=Array.isArray,S.parseJSON=JSON.parse,S.nodeName=C,S.isFunction=v,S.isWindow=y,S.camelCase=ie,S.type=x,S.now=Date.now,S.isNumeric=function(e){var t=S.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},S.trim=function(e){return null==e?"":(e+"").replace(rn,"$1")},void 0===(n=function(){return S}.apply(t,[]))||(e.exports=n);var on=r.jQuery,sn=r.$;return S.noConflict=function(e){return r.$===S&&(r.$=sn),e&&r.jQuery===S&&(r.jQuery=on),S},void 0===i&&(r.jQuery=r.$=S),S})},4782:(e,t,n)=>{"use strict";var r=n(6518),i=n(4376),o=n(3517),s=n(34),a=n(5610),l=n(6198),c=n(5397),u=n(4659),d=n(8227),p=n(597),f=n(7680),h=p("slice"),g=d("species"),m=Array,v=Math.max;r({target:"Array",proto:!0,forced:!h},{slice:function(e,t){var n,r,d,p=c(this),h=l(p),y=a(e,h),b=a(void 0===t?h:t,h);if(i(p)&&(n=p.constructor,(o(n)&&(n===m||i(n.prototype))||s(n)&&null===(n=n[g]))&&(n=void 0),n===m||void 0===n))return f(p,y,b);for(r=new(void 0===n?m:n)(v(b-y,0)),d=0;y<b;y++,d++)y in p&&u(r,d,p[y]);return r.length=d,r}})},4901:e=>{"use strict";var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},4913:(e,t,n)=>{"use strict";var r=n(3724),i=n(5917),o=n(8686),s=n(8551),a=n(6969),l=TypeError,c=Object.defineProperty,u=Object.getOwnPropertyDescriptor,d="enumerable",p="configurable",f="writable";t.f=r?o?function(e,t,n){if(s(e),t=a(t),s(n),"function"==typeof e&&"prototype"===t&&"value"in n&&f in n&&!n[f]){var r=u(e,t);r&&r[f]&&(e[t]=n.value,n={configurable:p in n?n[p]:r[p],enumerable:d in n?n[d]:r[d],writable:!1})}return c(e,t,n)}:c:function(e,t,n){if(s(e),t=a(t),s(n),i)try{return c(e,t,n)}catch(e){}if("get"in n||"set"in n)throw new l("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},5031:(e,t,n)=>{"use strict";var r=n(7751),i=n(9504),o=n(8480),s=n(3717),a=n(8551),l=i([].concat);e.exports=r("Reflect","ownKeys")||function(e){var t=o.f(a(e)),n=s.f;return n?l(t,n(e)):t}},5081:(e,t,n)=>{"use strict";var r=n(6518),i=n(4576);r({global:!0,forced:i.globalThis!==i},{globalThis:i})},5213:(e,t,n)=>{"use strict";var r=n(4576),i=n(9039),o=r.RegExp,s=!i(function(){var e=!0;try{o(".","d")}catch(t){e=!1}var t={},n="",r=e?"dgimsy":"gimsy",i=function(e,r){Object.defineProperty(t,e,{get:function(){return n+=r,!0}})},s={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var a in e&&(s.hasIndices="d"),s)i(a,s[a]);return Object.getOwnPropertyDescriptor(o.prototype,"flags").get.call(t)!==r||n!==r});e.exports={correct:s}},5397:(e,t,n)=>{"use strict";var r=n(7055),i=n(7750);e.exports=function(e){return r(i(e))}},5610:(e,t,n)=>{"use strict";var r=n(1291),i=Math.max,o=Math.min;e.exports=function(e,t){var n=r(e);return n<0?i(n+t,0):o(n,t)}},5745:(e,t,n)=>{"use strict";var r=n(7629);e.exports=function(e,t){return r[e]||(r[e]=t||{})}},5917:(e,t,n)=>{"use strict";var r=n(3724),i=n(9039),o=n(4055);e.exports=!r&&!i(function(){return 7!==Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a})},5966:(e,t,n)=>{"use strict";var r=n(9306),i=n(4117);e.exports=function(e,t){var n=e[t];return i(n)?void 0:r(n)}},6080:(e,t,n)=>{"use strict";var r=n(7476),i=n(9306),o=n(616),s=r(r.bind);e.exports=function(e,t){return i(e),void 0===t?e:o?s(e,t):function(){return e.apply(t,arguments)}}},6099:(e,t,n)=>{"use strict";var r=n(2140),i=n(6840),o=n(3179);r||i(Object.prototype,"toString",o,{unsafe:!0})},6119:(e,t,n)=>{"use strict";var r=n(5745),i=n(3392),o=r("keys");e.exports=function(e){return o[e]||(o[e]=i(e))}},6198:(e,t,n)=>{"use strict";var r=n(8014);e.exports=function(e){return r(e.length)}},6269:e=>{"use strict";e.exports={}},6319:(e,t,n)=>{"use strict";var r=n(8551),i=n(9539);e.exports=function(e,t,n,o){try{return o?t(r(n)[0],n[1]):t(n)}catch(t){i(e,"throw",t)}}},6395:e=>{"use strict";e.exports=!1},6469:(e,t,n)=>{"use strict";var r=n(8227),i=n(2360),o=n(4913).f,s=r("unscopables"),a=Array.prototype;void 0===a[s]&&o(a,s,{configurable:!0,value:i(null)}),e.exports=function(e){a[s][e]=!0}},6518:(e,t,n)=>{"use strict";var r=n(4576),i=n(7347).f,o=n(6699),s=n(6840),a=n(9433),l=n(7740),c=n(2796);e.exports=function(e,t){var n,u,d,p,f,h=e.target,g=e.global,m=e.stat;if(n=g?r:m?r[h]||a(h,{}):r[h]&&r[h].prototype)for(u in t){if(p=t[u],d=e.dontCallGetSet?(f=i(n,u))&&f.value:n[u],!c(g?u:h+(m?".":"#")+u,e.forced)&&void 0!==d){if(typeof p==typeof d)continue;l(p,d)}(e.sham||d&&d.sham)&&o(p,"sham",!0),s(n,u,p,e)}}},6699:(e,t,n)=>{"use strict";var r=n(3724),i=n(4913),o=n(6980);e.exports=r?function(e,t,n){return i.f(e,t,o(1,n))}:function(e,t,n){return e[t]=n,e}},6706:(e,t,n)=>{"use strict";var r=n(9504),i=n(9306);e.exports=function(e,t,n){try{return r(i(Object.getOwnPropertyDescriptor(e,t)[n]))}catch(e){}}},6761:(e,t,n)=>{"use strict";var r=n(6518),i=n(4576),o=n(9565),s=n(9504),a=n(6395),l=n(3724),c=n(4495),u=n(9039),d=n(9297),p=n(1625),f=n(8551),h=n(5397),g=n(6969),m=n(655),v=n(6980),y=n(2360),b=n(1072),_=n(8480),w=n(298),x=n(3717),O=n(7347),A=n(4913),S=n(6801),E=n(8773),C=n(6840),T=n(2106),k=n(5745),L=n(6119),j=n(421),I=n(3392),D=n(8227),P=n(1951),N=n(511),F=n(8242),M=n(687),$=n(1181),q=n(9213).forEach,H=L("hidden"),R="Symbol",B="prototype",W=$.set,z=$.getterFor(R),V=Object[B],U=i.Symbol,X=U&&U[B],K=i.RangeError,G=i.TypeError,Q=i.QObject,Y=O.f,J=A.f,Z=w.f,ee=E.f,te=s([].push),ne=k("symbols"),re=k("op-symbols"),ie=k("wks"),oe=!Q||!Q[B]||!Q[B].findChild,se=function(e,t,n){var r=Y(V,t);r&&delete V[t],J(e,t,n),r&&e!==V&&J(V,t,r)},ae=l&&u(function(){return 7!==y(J({},"a",{get:function(){return J(this,"a",{value:7}).a}})).a})?se:J,le=function(e,t){var n=ne[e]=y(X);return W(n,{type:R,tag:e,description:t}),l||(n.description=t),n},ce=function(e,t,n){e===V&&ce(re,t,n),f(e);var r=g(t);return f(n),d(ne,r)?(n.enumerable?(d(e,H)&&e[H][r]&&(e[H][r]=!1),n=y(n,{enumerable:v(0,!1)})):(d(e,H)||J(e,H,v(1,y(null))),e[H][r]=!0),ae(e,r,n)):J(e,r,n)},ue=function(e,t){f(e);var n=h(t),r=b(n).concat(he(n));return q(r,function(t){l&&!o(de,n,t)||ce(e,t,n[t])}),e},de=function(e){var t=g(e),n=o(ee,this,t);return!(this===V&&d(ne,t)&&!d(re,t))&&(!(n||!d(this,t)||!d(ne,t)||d(this,H)&&this[H][t])||n)},pe=function(e,t){var n=h(e),r=g(t);if(n!==V||!d(ne,r)||d(re,r)){var i=Y(n,r);return!i||!d(ne,r)||d(n,H)&&n[H][r]||(i.enumerable=!0),i}},fe=function(e){var t=Z(h(e)),n=[];return q(t,function(e){d(ne,e)||d(j,e)||te(n,e)}),n},he=function(e){var t=e===V,n=Z(t?re:h(e)),r=[];return q(n,function(e){!d(ne,e)||t&&!d(V,e)||te(r,ne[e])}),r};c||(C(X=(U=function(){if(p(X,this))throw new G("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?m(arguments[0]):void 0,t=I(e),n=function(e){var r=void 0===this?i:this;r===V&&o(n,re,e),d(r,H)&&d(r[H],t)&&(r[H][t]=!1);var s=v(1,e);try{ae(r,t,s)}catch(e){if(!(e instanceof K))throw e;se(r,t,s)}};return l&&oe&&ae(V,t,{configurable:!0,set:n}),le(t,e)})[B],"toString",function(){return z(this).tag}),C(U,"withoutSetter",function(e){return le(I(e),e)}),E.f=de,A.f=ce,S.f=ue,O.f=pe,_.f=w.f=fe,x.f=he,P.f=function(e){return le(D(e),e)},l&&(T(X,"description",{configurable:!0,get:function(){return z(this).description}}),a||C(V,"propertyIsEnumerable",de,{unsafe:!0}))),r({global:!0,constructor:!0,wrap:!0,forced:!c,sham:!c},{Symbol:U}),q(b(ie),function(e){N(e)}),r({target:R,stat:!0,forced:!c},{useSetter:function(){oe=!0},useSimple:function(){oe=!1}}),r({target:"Object",stat:!0,forced:!c,sham:!l},{create:function(e,t){return void 0===t?y(e):ue(y(e),t)},defineProperty:ce,defineProperties:ue,getOwnPropertyDescriptor:pe}),r({target:"Object",stat:!0,forced:!c},{getOwnPropertyNames:fe}),F(),M(U,R),j[H]=!0},6801:(e,t,n)=>{"use strict";var r=n(3724),i=n(8686),o=n(4913),s=n(8551),a=n(5397),l=n(1072);t.f=r&&!i?Object.defineProperties:function(e,t){s(e);for(var n,r=a(t),i=l(t),c=i.length,u=0;c>u;)o.f(e,n=i[u++],r[n]);return e}},6823:e=>{"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},6837:e=>{"use strict";var t=TypeError;e.exports=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e}},6840:(e,t,n)=>{"use strict";var r=n(4901),i=n(4913),o=n(283),s=n(9433);e.exports=function(e,t,n,a){a||(a={});var l=a.enumerable,c=void 0!==a.name?a.name:t;if(r(n)&&o(n,c,a),a.global)l?e[t]=n:s(t,n);else{try{a.unsafe?e[t]&&(l=!0):delete e[t]}catch(e){}l?e[t]=n:i.f(e,t,{value:n,enumerable:!1,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return e}},6933:(e,t,n)=>{"use strict";var r=n(9504),i=n(4376),o=n(4901),s=n(2195),a=n(655),l=r([].push);e.exports=function(e){if(o(e))return e;if(i(e)){for(var t=e.length,n=[],r=0;r<t;r++){var c=e[r];"string"==typeof c?l(n,c):"number"!=typeof c&&"Number"!==s(c)&&"String"!==s(c)||l(n,a(c))}var u=n.length,d=!0;return function(e,t){if(d)return d=!1,t;if(i(this))return t;for(var r=0;r<u;r++)if(n[r]===e)return t}}}},6955:(e,t,n)=>{"use strict";var r=n(2140),i=n(4901),o=n(2195),s=n(8227)("toStringTag"),a=Object,l="Arguments"===o(function(){return arguments}());e.exports=r?o:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(e){}}(t=a(e),s))?n:l?o(t):"Object"===(r=o(t))&&i(t.callee)?"Arguments":r}},6969:(e,t,n)=>{"use strict";var r=n(2777),i=n(757);e.exports=function(e){var t=r(e,"string");return i(t)?t:t+""}},6980:e=>{"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},7040:(e,t,n)=>{"use strict";var r=n(4495);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},7055:(e,t,n)=>{"use strict";var r=n(9504),i=n(9039),o=n(2195),s=Object,a=r("".split);e.exports=i(function(){return!s("z").propertyIsEnumerable(0)})?function(e){return"String"===o(e)?a(e,""):s(e)}:s},7323:(e,t,n)=>{"use strict";var r,i,o=n(9565),s=n(9504),a=n(655),l=n(7979),c=n(8429),u=n(5745),d=n(2360),p=n(1181).get,f=n(3635),h=n(8814),g=u("native-string-replace",String.prototype.replace),m=RegExp.prototype.exec,v=m,y=s("".charAt),b=s("".indexOf),_=s("".replace),w=s("".slice),x=(i=/b*/g,o(m,r=/a/,"a"),o(m,i,"a"),0!==r.lastIndex||0!==i.lastIndex),O=c.BROKEN_CARET,A=void 0!==/()??/.exec("")[1];(x||A||O||f||h)&&(v=function(e){var t,n,r,i,s,c,u,f=this,h=p(f),S=a(e),E=h.raw;if(E)return E.lastIndex=f.lastIndex,t=o(v,E,S),f.lastIndex=E.lastIndex,t;var C=h.groups,T=O&&f.sticky,k=o(l,f),L=f.source,j=0,I=S;if(T&&(k=_(k,"y",""),-1===b(k,"g")&&(k+="g"),I=w(S,f.lastIndex),f.lastIndex>0&&(!f.multiline||f.multiline&&"\n"!==y(S,f.lastIndex-1))&&(L="(?: "+L+")",I=" "+I,j++),n=new RegExp("^(?:"+L+")",k)),A&&(n=new RegExp("^"+L+"$(?!\\s)",k)),x&&(r=f.lastIndex),i=o(m,T?n:f,I),T?i?(i.input=w(i.input,j),i[0]=w(i[0],j),i.index=f.lastIndex,f.lastIndex+=i[0].length):f.lastIndex=0:x&&i&&(f.lastIndex=f.global?i.index+i[0].length:r),A&&i&&i.length>1&&o(g,i[0],n,function(){for(s=1;s<arguments.length-2;s++)void 0===arguments[s]&&(i[s]=void 0)}),i&&C)for(i.groups=c=d(null),s=0;s<C.length;s++)c[(u=C[s])[0]]=i[u[1]];return i}),e.exports=v},7347:(e,t,n)=>{"use strict";var r=n(3724),i=n(9565),o=n(8773),s=n(6980),a=n(5397),l=n(6969),c=n(9297),u=n(5917),d=Object.getOwnPropertyDescriptor;t.f=r?d:function(e,t){if(e=a(e),t=l(t),u)try{return d(e,t)}catch(e){}if(c(e,t))return s(!i(o.f,e,t),e[t])}},7400:e=>{"use strict";e.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},7433:(e,t,n)=>{"use strict";var r=n(4376),i=n(3517),o=n(34),s=n(8227)("species"),a=Array;e.exports=function(e){var t;return r(e)&&(t=e.constructor,(i(t)&&(t===a||r(t.prototype))||o(t)&&null===(t=t[s]))&&(t=void 0)),void 0===t?a:t}},7452:e=>{"use strict";e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},7476:(e,t,n)=>{"use strict";var r=n(2195),i=n(9504);e.exports=function(e){if("Function"===r(e))return i(e)}},7495:(e,t,n)=>{"use strict";var r=n(6518),i=n(7323);r({target:"RegExp",proto:!0,forced:/./.exec!==i},{exec:i})},7629:(e,t,n)=>{"use strict";var r=n(6395),i=n(4576),o=n(9433),s="__core-js_shared__",a=e.exports=i[s]||o(s,{});(a.versions||(a.versions=[])).push({version:"3.44.0",mode:r?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.44.0/LICENSE",source:"https://github.com/zloirock/core-js"})},7657:(e,t,n)=>{"use strict";var r,i,o,s=n(9039),a=n(4901),l=n(34),c=n(2360),u=n(2787),d=n(6840),p=n(8227),f=n(6395),h=p("iterator"),g=!1;[].keys&&("next"in(o=[].keys())?(i=u(u(o)))!==Object.prototype&&(r=i):g=!0),!l(r)||s(function(){var e={};return r[h].call(e)!==e})?r={}:f&&(r=c(r)),a(r[h])||d(r,h,function(){return this}),e.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:g}},7680:(e,t,n)=>{"use strict";var r=n(9504);e.exports=r([].slice)},7740:(e,t,n)=>{"use strict";var r=n(9297),i=n(5031),o=n(7347),s=n(4913);e.exports=function(e,t,n){for(var a=i(t),l=s.f,c=o.f,u=0;u<a.length;u++){var d=a[u];r(e,d)||n&&r(n,d)||l(e,d,c(t,d))}}},7750:(e,t,n)=>{"use strict";var r=n(4117),i=TypeError;e.exports=function(e){if(r(e))throw new i("Can't call method on "+e);return e}},7751:(e,t,n)=>{"use strict";var r=n(4576),i=n(4901);e.exports=function(e,t){return arguments.length<2?(n=r[e],i(n)?n:void 0):r[e]&&r[e][t];var n}},7764:(e,t,n)=>{"use strict";var r=n(8183).charAt,i=n(655),o=n(1181),s=n(1088),a=n(2529),l="String Iterator",c=o.set,u=o.getterFor(l);s(String,"String",function(e){c(this,{type:l,string:i(e),index:0})},function(){var e,t=u(this),n=t.string,i=t.index;return i>=n.length?a(void 0,!0):(e=r(n,i),t.index+=e.length,a(e,!1))})},7812:(e,t,n)=>{"use strict";var r=n(6518),i=n(9297),o=n(757),s=n(6823),a=n(5745),l=n(1296),c=a("symbol-to-string-registry");r({target:"Symbol",stat:!0,forced:!l},{keyFor:function(e){if(!o(e))throw new TypeError(s(e)+" is not a symbol");if(i(c,e))return c[e]}})},7916:(e,t,n)=>{"use strict";var r=n(6080),i=n(9565),o=n(8981),s=n(6319),a=n(4209),l=n(3517),c=n(6198),u=n(4659),d=n(81),p=n(851),f=Array;e.exports=function(e){var t=o(e),n=l(this),h=arguments.length,g=h>1?arguments[1]:void 0,m=void 0!==g;m&&(g=r(g,h>2?arguments[2]:void 0));var v,y,b,_,w,x,O=p(t),A=0;if(!O||this===f&&a(O))for(v=c(t),y=n?new this(v):f(v);v>A;A++)x=m?g(t[A],A):t[A],u(y,A,x);else for(y=n?new this:[],w=(_=d(t,O)).next;!(b=i(w,_)).done;A++)x=m?s(_,g,[b.value,A],!0):b.value,u(y,A,x);return y.length=A,y}},7979:(e,t,n)=>{"use strict";var r=n(8551);e.exports=function(){var e=r(this),t="";return e.hasIndices&&(t+="d"),e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.unicodeSets&&(t+="v"),e.sticky&&(t+="y"),t}},8014:(e,t,n)=>{"use strict";var r=n(1291),i=Math.min;e.exports=function(e){var t=r(e);return t>0?i(t,9007199254740991):0}},8183:(e,t,n)=>{"use strict";var r=n(9504),i=n(1291),o=n(655),s=n(7750),a=r("".charAt),l=r("".charCodeAt),c=r("".slice),u=function(e){return function(t,n){var r,u,d=o(s(t)),p=i(n),f=d.length;return p<0||p>=f?e?"":void 0:(r=l(d,p))<55296||r>56319||p+1===f||(u=l(d,p+1))<56320||u>57343?e?a(d,p):r:e?c(d,p,p+2):u-56320+(r-55296<<10)+65536}};e.exports={codeAt:u(!1),charAt:u(!0)}},8227:(e,t,n)=>{"use strict";var r=n(4576),i=n(5745),o=n(9297),s=n(3392),a=n(4495),l=n(7040),c=r.Symbol,u=i("wks"),d=l?c.for||c:c&&c.withoutSetter||s;e.exports=function(e){return o(u,e)||(u[e]=a&&o(c,e)?c[e]:d("Symbol."+e)),u[e]}},8242:(e,t,n)=>{"use strict";var r=n(9565),i=n(7751),o=n(8227),s=n(6840);e.exports=function(){var e=i("Symbol"),t=e&&e.prototype,n=t&&t.valueOf,a=o("toPrimitive");t&&!t[a]&&s(t,a,function(e){return r(n,this)},{arity:1})}},8429:(e,t,n)=>{"use strict";var r=n(9039),i=n(4576).RegExp,o=r(function(){var e=i("a","y");return e.lastIndex=2,null!==e.exec("abcd")}),s=o||r(function(){return!i("a","y").sticky}),a=o||r(function(){var e=i("^r","gy");return e.lastIndex=2,null!==e.exec("str")});e.exports={BROKEN_CARET:a,MISSED_STICKY:s,UNSUPPORTED_Y:o}},8480:(e,t,n)=>{"use strict";var r=n(1828),i=n(8727).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,i)}},8551:(e,t,n)=>{"use strict";var r=n(34),i=String,o=TypeError;e.exports=function(e){if(r(e))return e;throw new o(i(e)+" is not an object")}},8622:(e,t,n)=>{"use strict";var r=n(4576),i=n(4901),o=r.WeakMap;e.exports=i(o)&&/native code/.test(String(o))},8686:(e,t,n)=>{"use strict";var r=n(3724),i=n(9039);e.exports=r&&i(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})},8706:(e,t,n)=>{"use strict";var r=n(6518),i=n(9039),o=n(4376),s=n(34),a=n(8981),l=n(6198),c=n(6837),u=n(4659),d=n(1469),p=n(597),f=n(8227),h=n(9519),g=f("isConcatSpreadable"),m=h>=51||!i(function(){var e=[];return e[g]=!1,e.concat()[0]!==e}),v=function(e){if(!s(e))return!1;var t=e[g];return void 0!==t?!!t:o(e)};r({target:"Array",proto:!0,arity:1,forced:!m||!p("concat")},{concat:function(e){var t,n,r,i,o,s=a(this),p=d(s,0),f=0;for(t=-1,r=arguments.length;t<r;t++)if(v(o=-1===t?s:arguments[t]))for(i=l(o),c(f+i),n=0;n<i;n++,f++)n in o&&u(p,f,o[n]);else c(f+1),u(p,f++,o);return p.length=f,p}})},8727:e=>{"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8745:(e,t,n)=>{"use strict";var r=n(616),i=Function.prototype,o=i.apply,s=i.call;e.exports="object"==typeof Reflect&&Reflect.apply||(r?s.bind(o):function(){return s.apply(o,arguments)})},8773:(e,t)=>{"use strict";var n={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,i=r&&!n.call({1:2},1);t.f=i?function(e){var t=r(this,e);return!!t&&t.enumerable}:n},8781:(e,t,n)=>{"use strict";var r=n(350).PROPER,i=n(6840),o=n(8551),s=n(655),a=n(9039),l=n(1034),c="toString",u=RegExp.prototype,d=u[c],p=a(function(){return"/a/b"!==d.call({source:"a",flags:"b"})}),f=r&&d.name!==c;(p||f)&&i(u,c,function(){var e=o(this);return"/"+s(e.source)+"/"+s(l(e))},{unsafe:!0})},8814:(e,t,n)=>{"use strict";var r=n(9039),i=n(4576).RegExp;e.exports=r(function(){var e=i("(?<a>b)","g");return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")})},8981:(e,t,n)=>{"use strict";var r=n(7750),i=Object;e.exports=function(e){return i(r(e))}},9039:e=>{"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},9089:(e,t,n)=>{"use strict";var r=n(6518),i=n(9504),o=Date,s=i(o.prototype.getTime);r({target:"Date",stat:!0},{now:function(){return s(new o)}})},9167:(e,t,n)=>{"use strict";var r=n(4576);e.exports=r},9213:(e,t,n)=>{"use strict";var r=n(6080),i=n(9504),o=n(7055),s=n(8981),a=n(6198),l=n(1469),c=i([].push),u=function(e){var t=1===e,n=2===e,i=3===e,u=4===e,d=6===e,p=7===e,f=5===e||d;return function(h,g,m,v){for(var y,b,_=s(h),w=o(_),x=a(w),O=r(g,m),A=0,S=v||l,E=t?S(h,x):n||p?S(h,0):void 0;x>A;A++)if((f||A in w)&&(b=O(y=w[A],A,_),e))if(t)E[A]=b;else if(b)switch(e){case 3:return!0;case 5:return y;case 6:return A;case 2:c(E,y)}else switch(e){case 4:return!1;case 7:c(E,y)}return d?-1:i||u?u:E}};e.exports={forEach:u(0),map:u(1),filter:u(2),some:u(3),every:u(4),find:u(5),findIndex:u(6),filterReject:u(7)}},9296:(e,t,n)=>{"use strict";var r=n(4055)("span").classList,i=r&&r.constructor&&r.constructor.prototype;e.exports=i===Object.prototype?void 0:i},9297:(e,t,n)=>{"use strict";var r=n(9504),i=n(8981),o=r({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return o(i(e),t)}},9306:(e,t,n)=>{"use strict";var r=n(4901),i=n(6823),o=TypeError;e.exports=function(e){if(r(e))return e;throw new o(i(e)+" is not a function")}},9433:(e,t,n)=>{"use strict";var r=n(4576),i=Object.defineProperty;e.exports=function(e,t){try{i(r,e,{value:t,configurable:!0,writable:!0})}catch(n){r[e]=t}return t}},9463:(e,t,n)=>{"use strict";var r=n(6518),i=n(3724),o=n(4576),s=n(9504),a=n(9297),l=n(4901),c=n(1625),u=n(655),d=n(2106),p=n(7740),f=o.Symbol,h=f&&f.prototype;if(i&&l(f)&&(!("description"in h)||void 0!==f().description)){var g={},m=function(){var e=arguments.length<1||void 0===arguments[0]?void 0:u(arguments[0]),t=c(h,this)?new f(e):void 0===e?f():f(e);return""===e&&(g[t]=!0),t};p(m,f),m.prototype=h,h.constructor=m;var v="Symbol(description detection)"===String(f("description detection")),y=s(h.valueOf),b=s(h.toString),_=/^Symbol\((.*)\)[^)]+$/,w=s("".replace),x=s("".slice);d(h,"description",{configurable:!0,get:function(){var e=y(this);if(a(g,e))return"";var t=b(e),n=v?x(t,7,-1):w(t,_,"$1");return""===n?void 0:n}}),r({global:!0,constructor:!0,forced:!0},{Symbol:m})}},9504:(e,t,n)=>{"use strict";var r=n(616),i=Function.prototype,o=i.call,s=r&&i.bind.bind(o,o);e.exports=r?s:function(e){return function(){return o.apply(e,arguments)}}},9519:(e,t,n)=>{"use strict";var r,i,o=n(4576),s=n(2839),a=o.process,l=o.Deno,c=a&&a.versions||l&&l.version,u=c&&c.v8;u&&(i=(r=u.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1])),!i&&s&&(!(r=s.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=s.match(/Chrome\/(\d+)/))&&(i=+r[1]),e.exports=i},9539:(e,t,n)=>{"use strict";var r=n(9565),i=n(8551),o=n(5966);e.exports=function(e,t,n){var s,a;i(e);try{if(!(s=o(e,"return"))){if("throw"===t)throw n;return n}s=r(s,e)}catch(e){a=!0,s=e}if("throw"===t)throw n;if(a)throw s;return i(s),n}},9565:(e,t,n)=>{"use strict";var r=n(616),i=Function.prototype.call;e.exports=r?i.bind(i):function(){return i.apply(i,arguments)}},9617:(e,t,n)=>{"use strict";var r=n(5397),i=n(5610),o=n(6198),s=function(e){return function(t,n,s){var a=r(t),l=o(a);if(0===l)return!e&&-1;var c,u=i(s,l);if(e&&n!=n){for(;l>u;)if((c=a[u++])!=c)return!0}else for(;l>u;u++)if((e||u in a)&&a[u]===n)return e||u||0;return!e&&-1}};e.exports={includes:s(!0),indexOf:s(!1)}},9773:(e,t,n)=>{"use strict";var r=n(6518),i=n(4495),o=n(9039),s=n(3717),a=n(8981);r({target:"Object",stat:!0,forced:!i||o(function(){s.f(1)})},{getOwnPropertySymbols:function(e){var t=s.f;return t?t(a(e)):[]}})}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e={};n.r(e),n.d(e,{afterMain:()=>A,afterRead:()=>w,afterWrite:()=>C,applyStyles:()=>P,arrow:()=>ee,auto:()=>c,basePlacements:()=>u,beforeMain:()=>x,beforeRead:()=>b,beforeWrite:()=>S,bottom:()=>s,clippingParents:()=>f,computeStyles:()=>ie,createPopper:()=>Pe,createPopperBase:()=>De,createPopperLite:()=>Ne,detectOverflow:()=>_e,end:()=>p,eventListeners:()=>se,flip:()=>we,hide:()=>Ae,left:()=>l,main:()=>O,modifierPhases:()=>T,offset:()=>Se,placements:()=>y,popper:()=>g,popperGenerator:()=>Ie,popperOffsets:()=>Ee,preventOverflow:()=>Ce,read:()=>_,reference:()=>m,right:()=>a,start:()=>d,top:()=>o,variationPlacements:()=>v,viewport:()=>h,write:()=>E});var t={};n.r(t),n.d(t,{Alert:()=>jt,Button:()=>Dt,Carousel:()=>hn,Collapse:()=>Tn,Dropdown:()=>Zn,Modal:()=>Pr,Offcanvas:()=>Jr,Popover:()=>Ai,ScrollSpy:()=>Ni,Tab:()=>io,Toast:()=>_o,Tooltip:()=>bi});n(2675),n(9463),n(2259),n(8706),n(1629),n(3418),n(4346),n(3792),n(2062),n(4782),n(9089),n(3288),n(2010),n(6099),n(7495),n(8781),n(7764),n(2762),n(2480),n(3500),n(2953);var r=n(4692),i=n.n(r),o="top",s="bottom",a="right",l="left",c="auto",u=[o,s,a,l],d="start",p="end",f="clippingParents",h="viewport",g="popper",m="reference",v=u.reduce(function(e,t){return e.concat([t+"-"+d,t+"-"+p])},[]),y=[].concat(u,[c]).reduce(function(e,t){return e.concat([t,t+"-"+d,t+"-"+p])},[]),b="beforeRead",_="read",w="afterRead",x="beforeMain",O="main",A="afterMain",S="beforeWrite",E="write",C="afterWrite",T=[b,_,w,x,O,A,S,E,C];function k(e){return e?(e.nodeName||"").toLowerCase():null}function L(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function j(e){return e instanceof L(e).Element||e instanceof Element}function I(e){return e instanceof L(e).HTMLElement||e instanceof HTMLElement}function D(e){return"undefined"!=typeof ShadowRoot&&(e instanceof L(e).ShadowRoot||e instanceof ShadowRoot)}const P={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},i=t.elements[e];I(i)&&k(i)&&(Object.assign(i.style,n),Object.keys(r).forEach(function(e){var t=r[e];!1===t?i.removeAttribute(e):i.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(e){var r=t.elements[e],i=t.attributes[e]||{},o=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce(function(e,t){return e[t]="",e},{});I(r)&&k(r)&&(Object.assign(r.style,o),Object.keys(i).forEach(function(e){r.removeAttribute(e)}))})}},requires:["computeStyles"]};function N(e){return e.split("-")[0]}var F=Math.max,M=Math.min,$=Math.round;function q(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function H(){return!/^((?!chrome|android).)*safari/i.test(q())}function R(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),i=1,o=1;t&&I(e)&&(i=e.offsetWidth>0&&$(r.width)/e.offsetWidth||1,o=e.offsetHeight>0&&$(r.height)/e.offsetHeight||1);var s=(j(e)?L(e):window).visualViewport,a=!H()&&n,l=(r.left+(a&&s?s.offsetLeft:0))/i,c=(r.top+(a&&s?s.offsetTop:0))/o,u=r.width/i,d=r.height/o;return{width:u,height:d,top:c,right:l+u,bottom:c+d,left:l,x:l,y:c}}function B(e){var t=R(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function W(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&D(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function z(e){return L(e).getComputedStyle(e)}function V(e){return["table","td","th"].indexOf(k(e))>=0}function U(e){return((j(e)?e.ownerDocument:e.document)||window.document).documentElement}function X(e){return"html"===k(e)?e:e.assignedSlot||e.parentNode||(D(e)?e.host:null)||U(e)}function K(e){return I(e)&&"fixed"!==z(e).position?e.offsetParent:null}function G(e){for(var t=L(e),n=K(e);n&&V(n)&&"static"===z(n).position;)n=K(n);return n&&("html"===k(n)||"body"===k(n)&&"static"===z(n).position)?t:n||function(e){var t=/firefox/i.test(q());if(/Trident/i.test(q())&&I(e)&&"fixed"===z(e).position)return null;var n=X(e);for(D(n)&&(n=n.host);I(n)&&["html","body"].indexOf(k(n))<0;){var r=z(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function Q(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Y(e,t,n){return F(e,M(t,n))}function J(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Z(e,t){return t.reduce(function(t,n){return t[n]=e,t},{})}const ee={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,i=e.options,c=n.elements.arrow,d=n.modifiersData.popperOffsets,p=N(n.placement),f=Q(p),h=[l,a].indexOf(p)>=0?"height":"width";if(c&&d){var g=function(e,t){return J("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:Z(e,u))}(i.padding,n),m=B(c),v="y"===f?o:l,y="y"===f?s:a,b=n.rects.reference[h]+n.rects.reference[f]-d[f]-n.rects.popper[h],_=d[f]-n.rects.reference[f],w=G(c),x=w?"y"===f?w.clientHeight||0:w.clientWidth||0:0,O=b/2-_/2,A=g[v],S=x-m[h]-g[y],E=x/2-m[h]/2+O,C=Y(A,E,S),T=f;n.modifiersData[r]=((t={})[T]=C,t.centerOffset=C-E,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&W(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function te(e){return e.split("-")[1]}var ne={top:"auto",right:"auto",bottom:"auto",left:"auto"};function re(e){var t,n=e.popper,r=e.popperRect,i=e.placement,c=e.variation,u=e.offsets,d=e.position,f=e.gpuAcceleration,h=e.adaptive,g=e.roundOffsets,m=e.isFixed,v=u.x,y=void 0===v?0:v,b=u.y,_=void 0===b?0:b,w="function"==typeof g?g({x:y,y:_}):{x:y,y:_};y=w.x,_=w.y;var x=u.hasOwnProperty("x"),O=u.hasOwnProperty("y"),A=l,S=o,E=window;if(h){var C=G(n),T="clientHeight",k="clientWidth";if(C===L(n)&&"static"!==z(C=U(n)).position&&"absolute"===d&&(T="scrollHeight",k="scrollWidth"),i===o||(i===l||i===a)&&c===p)S=s,_-=(m&&C===E&&E.visualViewport?E.visualViewport.height:C[T])-r.height,_*=f?1:-1;if(i===l||(i===o||i===s)&&c===p)A=a,y-=(m&&C===E&&E.visualViewport?E.visualViewport.width:C[k])-r.width,y*=f?1:-1}var j,I=Object.assign({position:d},h&&ne),D=!0===g?function(e,t){var n=e.x,r=e.y,i=t.devicePixelRatio||1;return{x:$(n*i)/i||0,y:$(r*i)/i||0}}({x:y,y:_},L(n)):{x:y,y:_};return y=D.x,_=D.y,f?Object.assign({},I,((j={})[S]=O?"0":"",j[A]=x?"0":"",j.transform=(E.devicePixelRatio||1)<=1?"translate("+y+"px, "+_+"px)":"translate3d("+y+"px, "+_+"px, 0)",j)):Object.assign({},I,((t={})[S]=O?_+"px":"",t[A]=x?y+"px":"",t.transform="",t))}const ie={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,i=void 0===r||r,o=n.adaptive,s=void 0===o||o,a=n.roundOffsets,l=void 0===a||a,c={placement:N(t.placement),variation:te(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:i,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,re(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:s,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,re(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var oe={passive:!0};const se={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,i=r.scroll,o=void 0===i||i,s=r.resize,a=void 0===s||s,l=L(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&c.forEach(function(e){e.addEventListener("scroll",n.update,oe)}),a&&l.addEventListener("resize",n.update,oe),function(){o&&c.forEach(function(e){e.removeEventListener("scroll",n.update,oe)}),a&&l.removeEventListener("resize",n.update,oe)}},data:{}};var ae={left:"right",right:"left",bottom:"top",top:"bottom"};function le(e){return e.replace(/left|right|bottom|top/g,function(e){return ae[e]})}var ce={start:"end",end:"start"};function ue(e){return e.replace(/start|end/g,function(e){return ce[e]})}function de(e){var t=L(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function pe(e){return R(U(e)).left+de(e).scrollLeft}function fe(e){var t=z(e),n=t.overflow,r=t.overflowX,i=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+i+r)}function he(e){return["html","body","#document"].indexOf(k(e))>=0?e.ownerDocument.body:I(e)&&fe(e)?e:he(X(e))}function ge(e,t){var n;void 0===t&&(t=[]);var r=he(e),i=r===(null==(n=e.ownerDocument)?void 0:n.body),o=L(r),s=i?[o].concat(o.visualViewport||[],fe(r)?r:[]):r,a=t.concat(s);return i?a:a.concat(ge(X(s)))}function me(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function ve(e,t,n){return t===h?me(function(e,t){var n=L(e),r=U(e),i=n.visualViewport,o=r.clientWidth,s=r.clientHeight,a=0,l=0;if(i){o=i.width,s=i.height;var c=H();(c||!c&&"fixed"===t)&&(a=i.offsetLeft,l=i.offsetTop)}return{width:o,height:s,x:a+pe(e),y:l}}(e,n)):j(t)?function(e,t){var n=R(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):me(function(e){var t,n=U(e),r=de(e),i=null==(t=e.ownerDocument)?void 0:t.body,o=F(n.scrollWidth,n.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),s=F(n.scrollHeight,n.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),a=-r.scrollLeft+pe(e),l=-r.scrollTop;return"rtl"===z(i||n).direction&&(a+=F(n.clientWidth,i?i.clientWidth:0)-o),{width:o,height:s,x:a,y:l}}(U(e)))}function ye(e,t,n,r){var i="clippingParents"===t?function(e){var t=ge(X(e)),n=["absolute","fixed"].indexOf(z(e).position)>=0&&I(e)?G(e):e;return j(n)?t.filter(function(e){return j(e)&&W(e,n)&&"body"!==k(e)}):[]}(e):[].concat(t),o=[].concat(i,[n]),s=o[0],a=o.reduce(function(t,n){var i=ve(e,n,r);return t.top=F(i.top,t.top),t.right=M(i.right,t.right),t.bottom=M(i.bottom,t.bottom),t.left=F(i.left,t.left),t},ve(e,s,r));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function be(e){var t,n=e.reference,r=e.element,i=e.placement,c=i?N(i):null,u=i?te(i):null,f=n.x+n.width/2-r.width/2,h=n.y+n.height/2-r.height/2;switch(c){case o:t={x:f,y:n.y-r.height};break;case s:t={x:f,y:n.y+n.height};break;case a:t={x:n.x+n.width,y:h};break;case l:t={x:n.x-r.width,y:h};break;default:t={x:n.x,y:n.y}}var g=c?Q(c):null;if(null!=g){var m="y"===g?"height":"width";switch(u){case d:t[g]=t[g]-(n[m]/2-r[m]/2);break;case p:t[g]=t[g]+(n[m]/2-r[m]/2)}}return t}function _e(e,t){void 0===t&&(t={});var n=t,r=n.placement,i=void 0===r?e.placement:r,l=n.strategy,c=void 0===l?e.strategy:l,d=n.boundary,p=void 0===d?f:d,v=n.rootBoundary,y=void 0===v?h:v,b=n.elementContext,_=void 0===b?g:b,w=n.altBoundary,x=void 0!==w&&w,O=n.padding,A=void 0===O?0:O,S=J("number"!=typeof A?A:Z(A,u)),E=_===g?m:g,C=e.rects.popper,T=e.elements[x?E:_],k=ye(j(T)?T:T.contextElement||U(e.elements.popper),p,y,c),L=R(e.elements.reference),I=be({reference:L,element:C,strategy:"absolute",placement:i}),D=me(Object.assign({},C,I)),P=_===g?D:L,N={top:k.top-P.top+S.top,bottom:P.bottom-k.bottom+S.bottom,left:k.left-P.left+S.left,right:P.right-k.right+S.right},F=e.modifiersData.offset;if(_===g&&F){var M=F[i];Object.keys(N).forEach(function(e){var t=[a,s].indexOf(e)>=0?1:-1,n=[o,s].indexOf(e)>=0?"y":"x";N[e]+=M[n]*t})}return N}const we={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var i=n.mainAxis,p=void 0===i||i,f=n.altAxis,h=void 0===f||f,g=n.fallbackPlacements,m=n.padding,b=n.boundary,_=n.rootBoundary,w=n.altBoundary,x=n.flipVariations,O=void 0===x||x,A=n.allowedAutoPlacements,S=t.options.placement,E=N(S),C=g||(E===S||!O?[le(S)]:function(e){if(N(e)===c)return[];var t=le(e);return[ue(e),t,ue(t)]}(S)),T=[S].concat(C).reduce(function(e,n){return e.concat(N(n)===c?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,i=n.boundary,o=n.rootBoundary,s=n.padding,a=n.flipVariations,l=n.allowedAutoPlacements,c=void 0===l?y:l,d=te(r),p=d?a?v:v.filter(function(e){return te(e)===d}):u,f=p.filter(function(e){return c.indexOf(e)>=0});0===f.length&&(f=p);var h=f.reduce(function(t,n){return t[n]=_e(e,{placement:n,boundary:i,rootBoundary:o,padding:s})[N(n)],t},{});return Object.keys(h).sort(function(e,t){return h[e]-h[t]})}(t,{placement:n,boundary:b,rootBoundary:_,padding:m,flipVariations:O,allowedAutoPlacements:A}):n)},[]),k=t.rects.reference,L=t.rects.popper,j=new Map,I=!0,D=T[0],P=0;P<T.length;P++){var F=T[P],M=N(F),$=te(F)===d,q=[o,s].indexOf(M)>=0,H=q?"width":"height",R=_e(t,{placement:F,boundary:b,rootBoundary:_,altBoundary:w,padding:m}),B=q?$?a:l:$?s:o;k[H]>L[H]&&(B=le(B));var W=le(B),z=[];if(p&&z.push(R[M]<=0),h&&z.push(R[B]<=0,R[W]<=0),z.every(function(e){return e})){D=F,I=!1;break}j.set(F,z)}if(I)for(var V=function(e){var t=T.find(function(t){var n=j.get(t);if(n)return n.slice(0,e).every(function(e){return e})});if(t)return D=t,"break"},U=O?3:1;U>0;U--){if("break"===V(U))break}t.placement!==D&&(t.modifiersData[r]._skip=!0,t.placement=D,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function xe(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Oe(e){return[o,a,s,l].some(function(t){return e[t]>=0})}const Ae={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,i=t.rects.popper,o=t.modifiersData.preventOverflow,s=_e(t,{elementContext:"reference"}),a=_e(t,{altBoundary:!0}),l=xe(s,r),c=xe(a,i,o),u=Oe(l),d=Oe(c);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":d})}};const Se={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,i=n.offset,s=void 0===i?[0,0]:i,c=y.reduce(function(e,n){return e[n]=function(e,t,n){var r=N(e),i=[l,o].indexOf(r)>=0?-1:1,s="function"==typeof n?n(Object.assign({},t,{placement:e})):n,c=s[0],u=s[1];return c=c||0,u=(u||0)*i,[l,a].indexOf(r)>=0?{x:u,y:c}:{x:c,y:u}}(n,t.rects,s),e},{}),u=c[t.placement],d=u.x,p=u.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=d,t.modifiersData.popperOffsets.y+=p),t.modifiersData[r]=c}};const Ee={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=be({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}};const Ce={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,i=n.mainAxis,c=void 0===i||i,u=n.altAxis,p=void 0!==u&&u,f=n.boundary,h=n.rootBoundary,g=n.altBoundary,m=n.padding,v=n.tether,y=void 0===v||v,b=n.tetherOffset,_=void 0===b?0:b,w=_e(t,{boundary:f,rootBoundary:h,padding:m,altBoundary:g}),x=N(t.placement),O=te(t.placement),A=!O,S=Q(x),E="x"===S?"y":"x",C=t.modifiersData.popperOffsets,T=t.rects.reference,k=t.rects.popper,L="function"==typeof _?_(Object.assign({},t.rects,{placement:t.placement})):_,j="number"==typeof L?{mainAxis:L,altAxis:L}:Object.assign({mainAxis:0,altAxis:0},L),I=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,D={x:0,y:0};if(C){if(c){var P,$="y"===S?o:l,q="y"===S?s:a,H="y"===S?"height":"width",R=C[S],W=R+w[$],z=R-w[q],V=y?-k[H]/2:0,U=O===d?T[H]:k[H],X=O===d?-k[H]:-T[H],K=t.elements.arrow,J=y&&K?B(K):{width:0,height:0},Z=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},ee=Z[$],ne=Z[q],re=Y(0,T[H],J[H]),ie=A?T[H]/2-V-re-ee-j.mainAxis:U-re-ee-j.mainAxis,oe=A?-T[H]/2+V+re+ne+j.mainAxis:X+re+ne+j.mainAxis,se=t.elements.arrow&&G(t.elements.arrow),ae=se?"y"===S?se.clientTop||0:se.clientLeft||0:0,le=null!=(P=null==I?void 0:I[S])?P:0,ce=R+oe-le,ue=Y(y?M(W,R+ie-le-ae):W,R,y?F(z,ce):z);C[S]=ue,D[S]=ue-R}if(p){var de,pe="x"===S?o:l,fe="x"===S?s:a,he=C[E],ge="y"===E?"height":"width",me=he+w[pe],ve=he-w[fe],ye=-1!==[o,l].indexOf(x),be=null!=(de=null==I?void 0:I[E])?de:0,we=ye?me:he-T[ge]-k[ge]-be+j.altAxis,xe=ye?he+T[ge]+k[ge]-be-j.altAxis:ve,Oe=y&&ye?function(e,t,n){var r=Y(e,t,n);return r>n?n:r}(we,he,xe):Y(y?we:me,he,y?xe:ve);C[E]=Oe,D[E]=Oe-he}t.modifiersData[r]=D}},requiresIfExists:["offset"]};function Te(e,t,n){void 0===n&&(n=!1);var r,i,o=I(t),s=I(t)&&function(e){var t=e.getBoundingClientRect(),n=$(t.width)/e.offsetWidth||1,r=$(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),a=U(t),l=R(e,s,n),c={scrollLeft:0,scrollTop:0},u={x:0,y:0};return(o||!o&&!n)&&(("body"!==k(t)||fe(a))&&(c=(r=t)!==L(r)&&I(r)?{scrollLeft:(i=r).scrollLeft,scrollTop:i.scrollTop}:de(r)),I(t)?((u=R(t,!0)).x+=t.clientLeft,u.y+=t.clientTop):a&&(u.x=pe(a))),{x:l.left+c.scrollLeft-u.x,y:l.top+c.scrollTop-u.y,width:l.width,height:l.height}}function ke(e){var t=new Map,n=new Set,r=[];function i(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach(function(e){if(!n.has(e)){var r=t.get(e);r&&i(r)}}),r.push(e)}return e.forEach(function(e){t.set(e.name,e)}),e.forEach(function(e){n.has(e.name)||i(e)}),r}var Le={placement:"bottom",modifiers:[],strategy:"absolute"};function je(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}function Ie(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,i=t.defaultOptions,o=void 0===i?Le:i;return function(e,t,n){void 0===n&&(n=o);var i,s,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},Le,o),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},l=[],c=!1,u={state:a,setOptions:function(n){var i="function"==typeof n?n(a.options):n;d(),a.options=Object.assign({},o,a.options,i),a.scrollParents={reference:j(e)?ge(e):e.contextElement?ge(e.contextElement):[],popper:ge(t)};var s,c,p=function(e){var t=ke(e);return T.reduce(function(e,n){return e.concat(t.filter(function(e){return e.phase===n}))},[])}((s=[].concat(r,a.options.modifiers),c=s.reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{}),Object.keys(c).map(function(e){return c[e]})));return a.orderedModifiers=p.filter(function(e){return e.enabled}),a.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,i=e.effect;if("function"==typeof i){var o=i({state:a,name:t,instance:u,options:r}),s=function(){};l.push(o||s)}}),u.update()},forceUpdate:function(){if(!c){var e=a.elements,t=e.reference,n=e.popper;if(je(t,n)){a.rects={reference:Te(t,G(n),"fixed"===a.options.strategy),popper:B(n)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach(function(e){return a.modifiersData[e.name]=Object.assign({},e.data)});for(var r=0;r<a.orderedModifiers.length;r++)if(!0!==a.reset){var i=a.orderedModifiers[r],o=i.fn,s=i.options,l=void 0===s?{}:s,d=i.name;"function"==typeof o&&(a=o({state:a,options:l,name:d,instance:u})||a)}else a.reset=!1,r=-1}}},update:(i=function(){return new Promise(function(e){u.forceUpdate(),e(a)})},function(){return s||(s=new Promise(function(e){Promise.resolve().then(function(){s=void 0,e(i())})})),s}),destroy:function(){d(),c=!0}};if(!je(e,t))return u;function d(){l.forEach(function(e){return e()}),l=[]}return u.setOptions(n).then(function(e){!c&&n.onFirstUpdate&&n.onFirstUpdate(e)}),u}}var De=Ie(),Pe=Ie({defaultModifiers:[se,Ee,ie,P,Se,we,Ce,ee,Ae]}),Ne=Ie({defaultModifiers:[se,Ee,ie,P]}),Fe=n(4692);const Me=new Map,$e={set(e,t,n){Me.has(e)||Me.set(e,new Map);const r=Me.get(e);r.has(t)||0===r.size?r.set(t,n):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(r.keys())[0]}.`)},get:(e,t)=>Me.has(e)&&Me.get(e).get(t)||null,remove(e,t){if(!Me.has(e))return;const n=Me.get(e);n.delete(t),0===n.size&&Me.delete(e)}},qe="transitionend",He=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,(e,t)=>`#${CSS.escape(t)}`)),e),Re=e=>null==e?`${e}`:Object.prototype.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase(),Be=e=>{e.dispatchEvent(new Event(qe))},We=e=>!(!e||"object"!=typeof e)&&(void 0!==e.jquery&&(e=e[0]),void 0!==e.nodeType),ze=e=>We(e)?e.jquery?e[0]:e:"string"==typeof e&&e.length>0?document.querySelector(He(e)):null,Ve=e=>{if(!We(e)||0===e.getClientRects().length)return!1;const t="visible"===getComputedStyle(e).getPropertyValue("visibility"),n=e.closest("details:not([open])");if(!n)return t;if(n!==e){const t=e.closest("summary");if(t&&t.parentNode!==n)return!1;if(null===t)return!1}return t},Ue=e=>!e||e.nodeType!==Node.ELEMENT_NODE||(!!e.classList.contains("disabled")||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled"))),Xe=e=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){const t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?Xe(e.parentNode):null},Ke=()=>{},Ge=e=>{e.offsetHeight},Qe=()=>Fe&&!document.body.hasAttribute("data-bs-no-jquery")?Fe:null,Ye=[],Je=()=>"rtl"===document.documentElement.dir,Ze=e=>{var t;t=()=>{const t=Qe();if(t){const n=e.NAME,r=t.fn[n];t.fn[n]=e.jQueryInterface,t.fn[n].Constructor=e,t.fn[n].noConflict=()=>(t.fn[n]=r,e.jQueryInterface)}},"loading"===document.readyState?(Ye.length||document.addEventListener("DOMContentLoaded",()=>{for(const e of Ye)e()}),Ye.push(t)):t()},et=(e,t=[],n=e)=>"function"==typeof e?e.call(...t):n,tt=(e,t,n=!0)=>{if(!n)return void et(e);const r=(e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:n}=window.getComputedStyle(e);const r=Number.parseFloat(t),i=Number.parseFloat(n);return r||i?(t=t.split(",")[0],n=n.split(",")[0],1e3*(Number.parseFloat(t)+Number.parseFloat(n))):0})(t)+5;let i=!1;const o=({target:n})=>{n===t&&(i=!0,t.removeEventListener(qe,o),et(e))};t.addEventListener(qe,o),setTimeout(()=>{i||Be(t)},r)},nt=(e,t,n,r)=>{const i=e.length;let o=e.indexOf(t);return-1===o?!n&&r?e[i-1]:e[0]:(o+=n?1:-1,r&&(o=(o+i)%i),e[Math.max(0,Math.min(o,i-1))])},rt=/[^.]*(?=\..*)\.|.*/,it=/\..*/,ot=/::\d+$/,st={};let at=1;const lt={mouseenter:"mouseover",mouseleave:"mouseout"},ct=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function ut(e,t){return t&&`${t}::${at++}`||e.uidEvent||at++}function dt(e){const t=ut(e);return e.uidEvent=t,st[t]=st[t]||{},st[t]}function pt(e,t,n=null){return Object.values(e).find(e=>e.callable===t&&e.delegationSelector===n)}function ft(e,t,n){const r="string"==typeof t,i=r?n:t||n;let o=vt(e);return ct.has(o)||(o=e),[r,i,o]}function ht(e,t,n,r,i){if("string"!=typeof t||!e)return;let[o,s,a]=ft(t,n,r);if(t in lt){const e=e=>function(t){if(!t.relatedTarget||t.relatedTarget!==t.delegateTarget&&!t.delegateTarget.contains(t.relatedTarget))return e.call(this,t)};s=e(s)}const l=dt(e),c=l[a]||(l[a]={}),u=pt(c,s,o?n:null);if(u)return void(u.oneOff=u.oneOff&&i);const d=ut(s,t.replace(rt,"")),p=o?function(e,t,n){return function r(i){const o=e.querySelectorAll(t);for(let{target:s}=i;s&&s!==this;s=s.parentNode)for(const a of o)if(a===s)return bt(i,{delegateTarget:s}),r.oneOff&&yt.off(e,i.type,t,n),n.apply(s,[i])}}(e,n,s):function(e,t){return function n(r){return bt(r,{delegateTarget:e}),n.oneOff&&yt.off(e,r.type,t),t.apply(e,[r])}}(e,s);p.delegationSelector=o?n:null,p.callable=s,p.oneOff=i,p.uidEvent=d,c[d]=p,e.addEventListener(a,p,o)}function gt(e,t,n,r,i){const o=pt(t[n],r,i);o&&(e.removeEventListener(n,o,Boolean(i)),delete t[n][o.uidEvent])}function mt(e,t,n,r){const i=t[n]||{};for(const[o,s]of Object.entries(i))o.includes(r)&&gt(e,t,n,s.callable,s.delegationSelector)}function vt(e){return e=e.replace(it,""),lt[e]||e}const yt={on(e,t,n,r){ht(e,t,n,r,!1)},one(e,t,n,r){ht(e,t,n,r,!0)},off(e,t,n,r){if("string"!=typeof t||!e)return;const[i,o,s]=ft(t,n,r),a=s!==t,l=dt(e),c=l[s]||{},u=t.startsWith(".");if(void 0===o){if(u)for(const n of Object.keys(l))mt(e,l,n,t.slice(1));for(const[n,r]of Object.entries(c)){const i=n.replace(ot,"");a&&!t.includes(i)||gt(e,l,s,r.callable,r.delegationSelector)}}else{if(!Object.keys(c).length)return;gt(e,l,s,o,i?n:null)}},trigger(e,t,n){if("string"!=typeof t||!e)return null;const r=Qe();let i=null,o=!0,s=!0,a=!1;t!==vt(t)&&r&&(i=r.Event(t,n),r(e).trigger(i),o=!i.isPropagationStopped(),s=!i.isImmediatePropagationStopped(),a=i.isDefaultPrevented());const l=bt(new Event(t,{bubbles:o,cancelable:!0}),n);return a&&l.preventDefault(),s&&e.dispatchEvent(l),l.defaultPrevented&&i&&i.preventDefault(),l}};function bt(e,t={}){for(const[n,r]of Object.entries(t))try{e[n]=r}catch(t){Object.defineProperty(e,n,{configurable:!0,get:()=>r})}return e}function _t(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function wt(e){return e.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`)}const xt={setDataAttribute(e,t,n){e.setAttribute(`data-bs-${wt(t)}`,n)},removeDataAttribute(e,t){e.removeAttribute(`data-bs-${wt(t)}`)},getDataAttributes(e){if(!e)return{};const t={},n=Object.keys(e.dataset).filter(e=>e.startsWith("bs")&&!e.startsWith("bsConfig"));for(const r of n){let n=r.replace(/^bs/,"");n=n.charAt(0).toLowerCase()+n.slice(1),t[n]=_t(e.dataset[r])}return t},getDataAttribute:(e,t)=>_t(e.getAttribute(`data-bs-${wt(t)}`))};class Ot{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,t){const n=We(t)?xt.getDataAttribute(t,"config"):{};return{...this.constructor.Default,..."object"==typeof n?n:{},...We(t)?xt.getDataAttributes(t):{},..."object"==typeof e?e:{}}}_typeCheckConfig(e,t=this.constructor.DefaultType){for(const[n,r]of Object.entries(t)){const t=e[n],i=We(t)?"element":Re(t);if(!new RegExp(r).test(i))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${n}" provided type "${i}" but expected type "${r}".`)}}}class At extends Ot{constructor(e,t){super(),(e=ze(e))&&(this._element=e,this._config=this._getConfig(t),$e.set(this._element,this.constructor.DATA_KEY,this))}dispose(){$e.remove(this._element,this.constructor.DATA_KEY),yt.off(this._element,this.constructor.EVENT_KEY);for(const e of Object.getOwnPropertyNames(this))this[e]=null}_queueCallback(e,t,n=!0){tt(e,t,n)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return $e.get(ze(e),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,"object"==typeof t?t:null)}static get VERSION(){return"5.3.7"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}const St=e=>{let t=e.getAttribute("data-bs-target");if(!t||"#"===t){let n=e.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n=`#${n.split("#")[1]}`),t=n&&"#"!==n?n.trim():null}return t?t.split(",").map(e=>He(e)).join(","):null},Et={find:(e,t=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(t,e)),findOne:(e,t=document.documentElement)=>Element.prototype.querySelector.call(t,e),children:(e,t)=>[].concat(...e.children).filter(e=>e.matches(t)),parents(e,t){const n=[];let r=e.parentNode.closest(t);for(;r;)n.push(r),r=r.parentNode.closest(t);return n},prev(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return[n];n=n.nextElementSibling}return[]},focusableChildren(e){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(e=>`${e}:not([tabindex^="-"])`).join(",");return this.find(t,e).filter(e=>!Ue(e)&&Ve(e))},getSelectorFromElement(e){const t=St(e);return t&&Et.findOne(t)?t:null},getElementFromSelector(e){const t=St(e);return t?Et.findOne(t):null},getMultipleElementsFromSelector(e){const t=St(e);return t?Et.find(t):[]}},Ct=(e,t="hide")=>{const n=`click.dismiss${e.EVENT_KEY}`,r=e.NAME;yt.on(document,n,`[data-bs-dismiss="${r}"]`,function(n){if(["A","AREA"].includes(this.tagName)&&n.preventDefault(),Ue(this))return;const i=Et.getElementFromSelector(this)||this.closest(`.${r}`);e.getOrCreateInstance(i)[t]()})},Tt=".bs.alert",kt=`close${Tt}`,Lt=`closed${Tt}`;class jt extends At{static get NAME(){return"alert"}close(){if(yt.trigger(this._element,kt).defaultPrevented)return;this._element.classList.remove("show");const e=this._element.classList.contains("fade");this._queueCallback(()=>this._destroyElement(),this._element,e)}_destroyElement(){this._element.remove(),yt.trigger(this._element,Lt),this.dispose()}static jQueryInterface(e){return this.each(function(){const t=jt.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}})}}Ct(jt,"close"),Ze(jt);const It='[data-bs-toggle="button"]';class Dt extends At{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(e){return this.each(function(){const t=Dt.getOrCreateInstance(this);"toggle"===e&&t[e]()})}}yt.on(document,"click.bs.button.data-api",It,e=>{e.preventDefault();const t=e.target.closest(It);Dt.getOrCreateInstance(t).toggle()}),Ze(Dt);const Pt=".bs.swipe",Nt=`touchstart${Pt}`,Ft=`touchmove${Pt}`,Mt=`touchend${Pt}`,$t=`pointerdown${Pt}`,qt=`pointerup${Pt}`,Ht={endCallback:null,leftCallback:null,rightCallback:null},Rt={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class Bt extends Ot{constructor(e,t){super(),this._element=e,e&&Bt.isSupported()&&(this._config=this._getConfig(t),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return Ht}static get DefaultType(){return Rt}static get NAME(){return"swipe"}dispose(){yt.off(this._element,Pt)}_start(e){this._supportPointerEvents?this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX):this._deltaX=e.touches[0].clientX}_end(e){this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX-this._deltaX),this._handleSwipe(),et(this._config.endCallback)}_move(e){this._deltaX=e.touches&&e.touches.length>1?0:e.touches[0].clientX-this._deltaX}_handleSwipe(){const e=Math.abs(this._deltaX);if(e<=40)return;const t=e/this._deltaX;this._deltaX=0,t&&et(t>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(yt.on(this._element,$t,e=>this._start(e)),yt.on(this._element,qt,e=>this._end(e)),this._element.classList.add("pointer-event")):(yt.on(this._element,Nt,e=>this._start(e)),yt.on(this._element,Ft,e=>this._move(e)),yt.on(this._element,Mt,e=>this._end(e)))}_eventIsPointerPenTouch(e){return this._supportPointerEvents&&("pen"===e.pointerType||"touch"===e.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const Wt=".bs.carousel",zt=".data-api",Vt="ArrowLeft",Ut="ArrowRight",Xt="next",Kt="prev",Gt="left",Qt="right",Yt=`slide${Wt}`,Jt=`slid${Wt}`,Zt=`keydown${Wt}`,en=`mouseenter${Wt}`,tn=`mouseleave${Wt}`,nn=`dragstart${Wt}`,rn=`load${Wt}${zt}`,on=`click${Wt}${zt}`,sn="carousel",an="active",ln=".active",cn=".carousel-item",un=ln+cn,dn={[Vt]:Qt,[Ut]:Gt},pn={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},fn={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class hn extends At{constructor(e,t){super(e,t),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=Et.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===sn&&this.cycle()}static get Default(){return pn}static get DefaultType(){return fn}static get NAME(){return"carousel"}next(){this._slide(Xt)}nextWhenVisible(){!document.hidden&&Ve(this._element)&&this.next()}prev(){this._slide(Kt)}pause(){this._isSliding&&Be(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?yt.one(this._element,Jt,()=>this.cycle()):this.cycle())}to(e){const t=this._getItems();if(e>t.length-1||e<0)return;if(this._isSliding)return void yt.one(this._element,Jt,()=>this.to(e));const n=this._getItemIndex(this._getActive());if(n===e)return;const r=e>n?Xt:Kt;this._slide(r,t[e])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(e){return e.defaultInterval=e.interval,e}_addEventListeners(){this._config.keyboard&&yt.on(this._element,Zt,e=>this._keydown(e)),"hover"===this._config.pause&&(yt.on(this._element,en,()=>this.pause()),yt.on(this._element,tn,()=>this._maybeEnableCycle())),this._config.touch&&Bt.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const e of Et.find(".carousel-item img",this._element))yt.on(e,nn,e=>e.preventDefault());const e={leftCallback:()=>this._slide(this._directionToOrder(Gt)),rightCallback:()=>this._slide(this._directionToOrder(Qt)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),500+this._config.interval))}};this._swipeHelper=new Bt(this._element,e)}_keydown(e){if(/input|textarea/i.test(e.target.tagName))return;const t=dn[e.key];t&&(e.preventDefault(),this._slide(this._directionToOrder(t)))}_getItemIndex(e){return this._getItems().indexOf(e)}_setActiveIndicatorElement(e){if(!this._indicatorsElement)return;const t=Et.findOne(ln,this._indicatorsElement);t.classList.remove(an),t.removeAttribute("aria-current");const n=Et.findOne(`[data-bs-slide-to="${e}"]`,this._indicatorsElement);n&&(n.classList.add(an),n.setAttribute("aria-current","true"))}_updateInterval(){const e=this._activeElement||this._getActive();if(!e)return;const t=Number.parseInt(e.getAttribute("data-bs-interval"),10);this._config.interval=t||this._config.defaultInterval}_slide(e,t=null){if(this._isSliding)return;const n=this._getActive(),r=e===Xt,i=t||nt(this._getItems(),n,r,this._config.wrap);if(i===n)return;const o=this._getItemIndex(i),s=t=>yt.trigger(this._element,t,{relatedTarget:i,direction:this._orderToDirection(e),from:this._getItemIndex(n),to:o});if(s(Yt).defaultPrevented)return;if(!n||!i)return;const a=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=i;const l=r?"carousel-item-start":"carousel-item-end",c=r?"carousel-item-next":"carousel-item-prev";i.classList.add(c),Ge(i),n.classList.add(l),i.classList.add(l);this._queueCallback(()=>{i.classList.remove(l,c),i.classList.add(an),n.classList.remove(an,c,l),this._isSliding=!1,s(Jt)},n,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return Et.findOne(un,this._element)}_getItems(){return Et.find(cn,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(e){return Je()?e===Gt?Kt:Xt:e===Gt?Xt:Kt}_orderToDirection(e){return Je()?e===Kt?Gt:Qt:e===Kt?Qt:Gt}static jQueryInterface(e){return this.each(function(){const t=hn.getOrCreateInstance(this,e);if("number"!=typeof e){if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}}else t.to(e)})}}yt.on(document,on,"[data-bs-slide], [data-bs-slide-to]",function(e){const t=Et.getElementFromSelector(this);if(!t||!t.classList.contains(sn))return;e.preventDefault();const n=hn.getOrCreateInstance(t),r=this.getAttribute("data-bs-slide-to");return r?(n.to(r),void n._maybeEnableCycle()):"next"===xt.getDataAttribute(this,"slide")?(n.next(),void n._maybeEnableCycle()):(n.prev(),void n._maybeEnableCycle())}),yt.on(window,rn,()=>{const e=Et.find('[data-bs-ride="carousel"]');for(const t of e)hn.getOrCreateInstance(t)}),Ze(hn);const gn=".bs.collapse",mn=`show${gn}`,vn=`shown${gn}`,yn=`hide${gn}`,bn=`hidden${gn}`,_n=`click${gn}.data-api`,wn="show",xn="collapse",On="collapsing",An=`:scope .${xn} .${xn}`,Sn='[data-bs-toggle="collapse"]',En={parent:null,toggle:!0},Cn={parent:"(null|element)",toggle:"boolean"};class Tn extends At{constructor(e,t){super(e,t),this._isTransitioning=!1,this._triggerArray=[];const n=Et.find(Sn);for(const e of n){const t=Et.getSelectorFromElement(e),n=Et.find(t).filter(e=>e===this._element);null!==t&&n.length&&this._triggerArray.push(e)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return En}static get DefaultType(){return Cn}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(e=>e!==this._element).map(e=>Tn.getOrCreateInstance(e,{toggle:!1}))),e.length&&e[0]._isTransitioning)return;if(yt.trigger(this._element,mn).defaultPrevented)return;for(const t of e)t.hide();const t=this._getDimension();this._element.classList.remove(xn),this._element.classList.add(On),this._element.style[t]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const n=`scroll${t[0].toUpperCase()+t.slice(1)}`;this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(On),this._element.classList.add(xn,wn),this._element.style[t]="",yt.trigger(this._element,vn)},this._element,!0),this._element.style[t]=`${this._element[n]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(yt.trigger(this._element,yn).defaultPrevented)return;const e=this._getDimension();this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,Ge(this._element),this._element.classList.add(On),this._element.classList.remove(xn,wn);for(const e of this._triggerArray){const t=Et.getElementFromSelector(e);t&&!this._isShown(t)&&this._addAriaAndCollapsedClass([e],!1)}this._isTransitioning=!0;this._element.style[e]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(On),this._element.classList.add(xn),yt.trigger(this._element,bn)},this._element,!0)}_isShown(e=this._element){return e.classList.contains(wn)}_configAfterMerge(e){return e.toggle=Boolean(e.toggle),e.parent=ze(e.parent),e}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const e=this._getFirstLevelChildren(Sn);for(const t of e){const e=Et.getElementFromSelector(t);e&&this._addAriaAndCollapsedClass([t],this._isShown(e))}}_getFirstLevelChildren(e){const t=Et.find(An,this._config.parent);return Et.find(e,this._config.parent).filter(e=>!t.includes(e))}_addAriaAndCollapsedClass(e,t){if(e.length)for(const n of e)n.classList.toggle("collapsed",!t),n.setAttribute("aria-expanded",t)}static jQueryInterface(e){const t={};return"string"==typeof e&&/show|hide/.test(e)&&(t.toggle=!1),this.each(function(){const n=Tn.getOrCreateInstance(this,t);if("string"==typeof e){if(void 0===n[e])throw new TypeError(`No method named "${e}"`);n[e]()}})}}yt.on(document,_n,Sn,function(e){("A"===e.target.tagName||e.delegateTarget&&"A"===e.delegateTarget.tagName)&&e.preventDefault();for(const e of Et.getMultipleElementsFromSelector(this))Tn.getOrCreateInstance(e,{toggle:!1}).toggle()}),Ze(Tn);const kn="dropdown",Ln=".bs.dropdown",jn=".data-api",In="ArrowUp",Dn="ArrowDown",Pn=`hide${Ln}`,Nn=`hidden${Ln}`,Fn=`show${Ln}`,Mn=`shown${Ln}`,$n=`click${Ln}${jn}`,qn=`keydown${Ln}${jn}`,Hn=`keyup${Ln}${jn}`,Rn="show",Bn='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',Wn=`${Bn}.${Rn}`,zn=".dropdown-menu",Vn=Je()?"top-end":"top-start",Un=Je()?"top-start":"top-end",Xn=Je()?"bottom-end":"bottom-start",Kn=Je()?"bottom-start":"bottom-end",Gn=Je()?"left-start":"right-start",Qn=Je()?"right-start":"left-start",Yn={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},Jn={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class Zn extends At{constructor(e,t){super(e,t),this._popper=null,this._parent=this._element.parentNode,this._menu=Et.next(this._element,zn)[0]||Et.prev(this._element,zn)[0]||Et.findOne(zn,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Yn}static get DefaultType(){return Jn}static get NAME(){return kn}toggle(){return this._isShown()?this.hide():this.show()}show(){if(Ue(this._element)||this._isShown())return;const e={relatedTarget:this._element};if(!yt.trigger(this._element,Fn,e).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const e of[].concat(...document.body.children))yt.on(e,"mouseover",Ke);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Rn),this._element.classList.add(Rn),yt.trigger(this._element,Mn,e)}}hide(){if(Ue(this._element)||!this._isShown())return;const e={relatedTarget:this._element};this._completeHide(e)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(e){if(!yt.trigger(this._element,Pn,e).defaultPrevented){if("ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))yt.off(e,"mouseover",Ke);this._popper&&this._popper.destroy(),this._menu.classList.remove(Rn),this._element.classList.remove(Rn),this._element.setAttribute("aria-expanded","false"),xt.removeDataAttribute(this._menu,"popper"),yt.trigger(this._element,Nn,e),this._element.focus()}}_getConfig(e){if("object"==typeof(e=super._getConfig(e)).reference&&!We(e.reference)&&"function"!=typeof e.reference.getBoundingClientRect)throw new TypeError(`${kn.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return e}_createPopper(){let e=this._element;"parent"===this._config.reference?e=this._parent:We(this._config.reference)?e=ze(this._config.reference):"object"==typeof this._config.reference&&(e=this._config.reference);const t=this._getPopperConfig();this._popper=Pe(e,this._menu,t)}_isShown(){return this._menu.classList.contains(Rn)}_getPlacement(){const e=this._parent;if(e.classList.contains("dropend"))return Gn;if(e.classList.contains("dropstart"))return Qn;if(e.classList.contains("dropup-center"))return"top";if(e.classList.contains("dropdown-center"))return"bottom";const t="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return e.classList.contains("dropup")?t?Un:Vn:t?Kn:Xn}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:e}=this._config;return"string"==typeof e?e.split(",").map(e=>Number.parseInt(e,10)):"function"==typeof e?t=>e(t,this._element):e}_getPopperConfig(){const e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(xt.setDataAttribute(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),{...e,...et(this._config.popperConfig,[void 0,e])}}_selectMenuItem({key:e,target:t}){const n=Et.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(e=>Ve(e));n.length&&nt(n,t,e===Dn,!n.includes(t)).focus()}static jQueryInterface(e){return this.each(function(){const t=Zn.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}static clearMenus(e){if(2===e.button||"keyup"===e.type&&"Tab"!==e.key)return;const t=Et.find(Wn);for(const n of t){const t=Zn.getInstance(n);if(!t||!1===t._config.autoClose)continue;const r=e.composedPath(),i=r.includes(t._menu);if(r.includes(t._element)||"inside"===t._config.autoClose&&!i||"outside"===t._config.autoClose&&i)continue;if(t._menu.contains(e.target)&&("keyup"===e.type&&"Tab"===e.key||/input|select|option|textarea|form/i.test(e.target.tagName)))continue;const o={relatedTarget:t._element};"click"===e.type&&(o.clickEvent=e),t._completeHide(o)}}static dataApiKeydownHandler(e){const t=/input|textarea/i.test(e.target.tagName),n="Escape"===e.key,r=[In,Dn].includes(e.key);if(!r&&!n)return;if(t&&!n)return;e.preventDefault();const i=this.matches(Bn)?this:Et.prev(this,Bn)[0]||Et.next(this,Bn)[0]||Et.findOne(Bn,e.delegateTarget.parentNode),o=Zn.getOrCreateInstance(i);if(r)return e.stopPropagation(),o.show(),void o._selectMenuItem(e);o._isShown()&&(e.stopPropagation(),o.hide(),i.focus())}}yt.on(document,qn,Bn,Zn.dataApiKeydownHandler),yt.on(document,qn,zn,Zn.dataApiKeydownHandler),yt.on(document,$n,Zn.clearMenus),yt.on(document,Hn,Zn.clearMenus),yt.on(document,$n,Bn,function(e){e.preventDefault(),Zn.getOrCreateInstance(this).toggle()}),Ze(Zn);const er="backdrop",tr="show",nr=`mousedown.bs.${er}`,rr={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},ir={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class or extends Ot{constructor(e){super(),this._config=this._getConfig(e),this._isAppended=!1,this._element=null}static get Default(){return rr}static get DefaultType(){return ir}static get NAME(){return er}show(e){if(!this._config.isVisible)return void et(e);this._append();const t=this._getElement();this._config.isAnimated&&Ge(t),t.classList.add(tr),this._emulateAnimation(()=>{et(e)})}hide(e){this._config.isVisible?(this._getElement().classList.remove(tr),this._emulateAnimation(()=>{this.dispose(),et(e)})):et(e)}dispose(){this._isAppended&&(yt.off(this._element,nr),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e}return this._element}_configAfterMerge(e){return e.rootElement=ze(e.rootElement),e}_append(){if(this._isAppended)return;const e=this._getElement();this._config.rootElement.append(e),yt.on(e,nr,()=>{et(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(e){tt(e,this._getElement(),this._config.isAnimated)}}const sr=".bs.focustrap",ar=`focusin${sr}`,lr=`keydown.tab${sr}`,cr="backward",ur={autofocus:!0,trapElement:null},dr={autofocus:"boolean",trapElement:"element"};class pr extends Ot{constructor(e){super(),this._config=this._getConfig(e),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return ur}static get DefaultType(){return dr}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),yt.off(document,sr),yt.on(document,ar,e=>this._handleFocusin(e)),yt.on(document,lr,e=>this._handleKeydown(e)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,yt.off(document,sr))}_handleFocusin(e){const{trapElement:t}=this._config;if(e.target===document||e.target===t||t.contains(e.target))return;const n=Et.focusableChildren(t);0===n.length?t.focus():this._lastTabNavDirection===cr?n[n.length-1].focus():n[0].focus()}_handleKeydown(e){"Tab"===e.key&&(this._lastTabNavDirection=e.shiftKey?cr:"forward")}}const fr=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",hr=".sticky-top",gr="padding-right",mr="margin-right";class vr{constructor(){this._element=document.body}getWidth(){const e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){const e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,gr,t=>t+e),this._setElementAttributes(fr,gr,t=>t+e),this._setElementAttributes(hr,mr,t=>t-e)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,gr),this._resetElementAttributes(fr,gr),this._resetElementAttributes(hr,mr)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,t,n){const r=this.getWidth();this._applyManipulationCallback(e,e=>{if(e!==this._element&&window.innerWidth>e.clientWidth+r)return;this._saveInitialAttribute(e,t);const i=window.getComputedStyle(e).getPropertyValue(t);e.style.setProperty(t,`${n(Number.parseFloat(i))}px`)})}_saveInitialAttribute(e,t){const n=e.style.getPropertyValue(t);n&&xt.setDataAttribute(e,t,n)}_resetElementAttributes(e,t){this._applyManipulationCallback(e,e=>{const n=xt.getDataAttribute(e,t);null!==n?(xt.removeDataAttribute(e,t),e.style.setProperty(t,n)):e.style.removeProperty(t)})}_applyManipulationCallback(e,t){if(We(e))t(e);else for(const n of Et.find(e,this._element))t(n)}}const yr=".bs.modal",br=`hide${yr}`,_r=`hidePrevented${yr}`,wr=`hidden${yr}`,xr=`show${yr}`,Or=`shown${yr}`,Ar=`resize${yr}`,Sr=`click.dismiss${yr}`,Er=`mousedown.dismiss${yr}`,Cr=`keydown.dismiss${yr}`,Tr=`click${yr}.data-api`,kr="modal-open",Lr="show",jr="modal-static",Ir={backdrop:!0,focus:!0,keyboard:!0},Dr={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Pr extends At{constructor(e,t){super(e,t),this._dialog=Et.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new vr,this._addEventListeners()}static get Default(){return Ir}static get DefaultType(){return Dr}static get NAME(){return"modal"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){if(this._isShown||this._isTransitioning)return;yt.trigger(this._element,xr,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(kr),this._adjustDialog(),this._backdrop.show(()=>this._showElement(e)))}hide(){if(!this._isShown||this._isTransitioning)return;yt.trigger(this._element,br).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Lr),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){yt.off(window,yr),yt.off(this._dialog,yr),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new or({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new pr({trapElement:this._element})}_showElement(e){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const t=Et.findOne(".modal-body",this._dialog);t&&(t.scrollTop=0),Ge(this._element),this._element.classList.add(Lr);this._queueCallback(()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,yt.trigger(this._element,Or,{relatedTarget:e})},this._dialog,this._isAnimated())}_addEventListeners(){yt.on(this._element,Cr,e=>{"Escape"===e.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())}),yt.on(window,Ar,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),yt.on(this._element,Er,e=>{yt.one(this._element,Sr,t=>{this._element===e.target&&this._element===t.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(kr),this._resetAdjustments(),this._scrollBar.reset(),yt.trigger(this._element,wr)})}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(yt.trigger(this._element,_r).defaultPrevented)return;const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._element.style.overflowY;"hidden"===t||this._element.classList.contains(jr)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(jr),this._queueCallback(()=>{this._element.classList.remove(jr),this._queueCallback(()=>{this._element.style.overflowY=t},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),n=t>0;if(n&&!e){const e=Je()?"paddingLeft":"paddingRight";this._element.style[e]=`${t}px`}if(!n&&e){const e=Je()?"paddingRight":"paddingLeft";this._element.style[e]=`${t}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,t){return this.each(function(){const n=Pr.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===n[e])throw new TypeError(`No method named "${e}"`);n[e](t)}})}}yt.on(document,Tr,'[data-bs-toggle="modal"]',function(e){const t=Et.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),yt.one(t,xr,e=>{e.defaultPrevented||yt.one(t,wr,()=>{Ve(this)&&this.focus()})});const n=Et.findOne(".modal.show");n&&Pr.getInstance(n).hide();Pr.getOrCreateInstance(t).toggle(this)}),Ct(Pr),Ze(Pr);const Nr=".bs.offcanvas",Fr=".data-api",Mr=`load${Nr}${Fr}`,$r="show",qr="showing",Hr="hiding",Rr=".offcanvas.show",Br=`show${Nr}`,Wr=`shown${Nr}`,zr=`hide${Nr}`,Vr=`hidePrevented${Nr}`,Ur=`hidden${Nr}`,Xr=`resize${Nr}`,Kr=`click${Nr}${Fr}`,Gr=`keydown.dismiss${Nr}`,Qr={backdrop:!0,keyboard:!0,scroll:!1},Yr={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class Jr extends At{constructor(e,t){super(e,t),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Qr}static get DefaultType(){return Yr}static get NAME(){return"offcanvas"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){if(this._isShown)return;if(yt.trigger(this._element,Br,{relatedTarget:e}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||(new vr).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(qr);this._queueCallback(()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add($r),this._element.classList.remove(qr),yt.trigger(this._element,Wr,{relatedTarget:e})},this._element,!0)}hide(){if(!this._isShown)return;if(yt.trigger(this._element,zr).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(Hr),this._backdrop.hide();this._queueCallback(()=>{this._element.classList.remove($r,Hr),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new vr).reset(),yt.trigger(this._element,Ur)},this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const e=Boolean(this._config.backdrop);return new or({className:"offcanvas-backdrop",isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?()=>{"static"!==this._config.backdrop?this.hide():yt.trigger(this._element,Vr)}:null})}_initializeFocusTrap(){return new pr({trapElement:this._element})}_addEventListeners(){yt.on(this._element,Gr,e=>{"Escape"===e.key&&(this._config.keyboard?this.hide():yt.trigger(this._element,Vr))})}static jQueryInterface(e){return this.each(function(){const t=Jr.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}})}}yt.on(document,Kr,'[data-bs-toggle="offcanvas"]',function(e){const t=Et.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&e.preventDefault(),Ue(this))return;yt.one(t,Ur,()=>{Ve(this)&&this.focus()});const n=Et.findOne(Rr);n&&n!==t&&Jr.getInstance(n).hide();Jr.getOrCreateInstance(t).toggle(this)}),yt.on(window,Mr,()=>{for(const e of Et.find(Rr))Jr.getOrCreateInstance(e).show()}),yt.on(window,Xr,()=>{for(const e of Et.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(e).position&&Jr.getOrCreateInstance(e).hide()}),Ct(Jr),Ze(Jr);const Zr={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},ei=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),ti=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,ni=(e,t)=>{const n=e.nodeName.toLowerCase();return t.includes(n)?!ei.has(n)||Boolean(ti.test(e.nodeValue)):t.filter(e=>e instanceof RegExp).some(e=>e.test(n))};const ri={allowList:Zr,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},ii={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},oi={entry:"(string|element|function|null)",selector:"(string|element)"};class si extends Ot{constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return ri}static get DefaultType(){return ii}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map(e=>this._resolvePossibleFunction(e)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){const e=document.createElement("div");e.innerHTML=this._maybeSanitize(this._config.template);for(const[t,n]of Object.entries(this._config.content))this._setContent(e,n,t);const t=e.children[0],n=this._resolvePossibleFunction(this._config.extraClass);return n&&t.classList.add(...n.split(" ")),t}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(const[t,n]of Object.entries(e))super._typeCheckConfig({selector:t,entry:n},oi)}_setContent(e,t,n){const r=Et.findOne(n,e);r&&((t=this._resolvePossibleFunction(t))?We(t)?this._putElementInTemplate(ze(t),r):this._config.html?r.innerHTML=this._maybeSanitize(t):r.textContent=t:r.remove())}_maybeSanitize(e){return this._config.sanitize?function(e,t,n){if(!e.length)return e;if(n&&"function"==typeof n)return n(e);const r=(new window.DOMParser).parseFromString(e,"text/html"),i=[].concat(...r.body.querySelectorAll("*"));for(const e of i){const n=e.nodeName.toLowerCase();if(!Object.keys(t).includes(n)){e.remove();continue}const r=[].concat(...e.attributes),i=[].concat(t["*"]||[],t[n]||[]);for(const t of r)ni(t,i)||e.removeAttribute(t.nodeName)}return r.body.innerHTML}(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return et(e,[void 0,this])}_putElementInTemplate(e,t){if(this._config.html)return t.innerHTML="",void t.append(e);t.textContent=e.textContent}}const ai=new Set(["sanitize","allowList","sanitizeFn"]),li="fade",ci="show",ui=".tooltip-inner",di=".modal",pi="hide.bs.modal",fi="hover",hi="focus",gi="click",mi={AUTO:"auto",TOP:"top",RIGHT:Je()?"left":"right",BOTTOM:"bottom",LEFT:Je()?"right":"left"},vi={allowList:Zr,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},yi={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class bi extends At{constructor(e,t){super(e,t),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return vi}static get DefaultType(){return yi}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),yt.off(this._element.closest(di),pi,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const e=yt.trigger(this._element,this.constructor.eventName("show")),t=(Xe(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!t)return;this._disposePopper();const n=this._getTipElement();this._element.setAttribute("aria-describedby",n.getAttribute("id"));const{container:r}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(r.append(n),yt.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(n),n.classList.add(ci),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))yt.on(e,"mouseover",Ke);this._queueCallback(()=>{yt.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1},this.tip,this._isAnimated())}hide(){if(!this._isShown())return;if(yt.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented)return;if(this._getTipElement().classList.remove(ci),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))yt.off(e,"mouseover",Ke);this._activeTrigger[gi]=!1,this._activeTrigger[hi]=!1,this._activeTrigger[fi]=!1,this._isHovered=null;this._queueCallback(()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),yt.trigger(this._element,this.constructor.eventName("hidden")))},this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){const t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove(li,ci),t.classList.add(`bs-${this.constructor.NAME}-auto`);const n=(e=>{do{e+=Math.floor(1e6*Math.random())}while(document.getElementById(e));return e})(this.constructor.NAME).toString();return t.setAttribute("id",n),this._isAnimated()&&t.classList.add(li),t}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new si({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[ui]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(li)}_isShown(){return this.tip&&this.tip.classList.contains(ci)}_createPopper(e){const t=et(this._config.placement,[this,e,this._element]),n=mi[t.toUpperCase()];return Pe(this._element,e,this._getPopperConfig(n))}_getOffset(){const{offset:e}=this._config;return"string"==typeof e?e.split(",").map(e=>Number.parseInt(e,10)):"function"==typeof e?t=>e(t,this._element):e}_resolvePossibleFunction(e){return et(e,[this._element,this._element])}_getPopperConfig(e){const t={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:e=>{this._getTipElement().setAttribute("data-popper-placement",e.state.placement)}}]};return{...t,...et(this._config.popperConfig,[void 0,t])}}_setListeners(){const e=this._config.trigger.split(" ");for(const t of e)if("click"===t)yt.on(this._element,this.constructor.eventName("click"),this._config.selector,e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger[gi]=!(t._isShown()&&t._activeTrigger[gi]),t.toggle()});else if("manual"!==t){const e=t===fi?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),n=t===fi?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");yt.on(this._element,e,this._config.selector,e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusin"===e.type?hi:fi]=!0,t._enter()}),yt.on(this._element,n,this._config.selector,e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusout"===e.type?hi:fi]=t._element.contains(e.relatedTarget),t._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},yt.on(this._element.closest(di),pi,this._hideModalHandler)}_fixTitle(){const e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){const t=xt.getDataAttributes(this._element);for(const e of Object.keys(t))ai.has(e)&&delete t[e];return e={...t,..."object"==typeof e&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=!1===e.container?document.body:ze(e.container),"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),e}_getDelegateConfig(){const e={};for(const[t,n]of Object.entries(this._config))this.constructor.Default[t]!==n&&(e[t]=n);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each(function(){const t=bi.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}}Ze(bi);const _i=".popover-header",wi=".popover-body",xi={...bi.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},Oi={...bi.DefaultType,content:"(null|string|element|function)"};class Ai extends bi{static get Default(){return xi}static get DefaultType(){return Oi}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[_i]:this._getTitle(),[wi]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(e){return this.each(function(){const t=Ai.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}}Ze(Ai);const Si=".bs.scrollspy",Ei=`activate${Si}`,Ci=`click${Si}`,Ti=`load${Si}.data-api`,ki="active",Li="[href]",ji=".nav-link",Ii=`${ji}, .nav-item > ${ji}, .list-group-item`,Di={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Pi={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Ni extends At{constructor(e,t){super(e,t),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Di}static get DefaultType(){return Pi}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const e of this._observableSections.values())this._observer.observe(e)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(e){return e.target=ze(e.target)||document.body,e.rootMargin=e.offset?`${e.offset}px 0px -30%`:e.rootMargin,"string"==typeof e.threshold&&(e.threshold=e.threshold.split(",").map(e=>Number.parseFloat(e))),e}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(yt.off(this._config.target,Ci),yt.on(this._config.target,Ci,Li,e=>{const t=this._observableSections.get(e.target.hash);if(t){e.preventDefault();const n=this._rootElement||window,r=t.offsetTop-this._element.offsetTop;if(n.scrollTo)return void n.scrollTo({top:r,behavior:"smooth"});n.scrollTop=r}}))}_getNewObserver(){const e={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(e=>this._observerCallback(e),e)}_observerCallback(e){const t=e=>this._targetLinks.get(`#${e.target.id}`),n=e=>{this._previousScrollData.visibleEntryTop=e.target.offsetTop,this._process(t(e))},r=(this._rootElement||document.documentElement).scrollTop,i=r>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=r;for(const o of e){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(t(o));continue}const e=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(i&&e){if(n(o),!r)return}else i||e||n(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const e=Et.find(Li,this._config.target);for(const t of e){if(!t.hash||Ue(t))continue;const e=Et.findOne(decodeURI(t.hash),this._element);Ve(e)&&(this._targetLinks.set(decodeURI(t.hash),t),this._observableSections.set(t.hash,e))}}_process(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),this._activeTarget=e,e.classList.add(ki),this._activateParents(e),yt.trigger(this._element,Ei,{relatedTarget:e}))}_activateParents(e){if(e.classList.contains("dropdown-item"))Et.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add(ki);else for(const t of Et.parents(e,".nav, .list-group"))for(const e of Et.prev(t,Ii))e.classList.add(ki)}_clearActiveClass(e){e.classList.remove(ki);const t=Et.find(`${Li}.${ki}`,e);for(const e of t)e.classList.remove(ki)}static jQueryInterface(e){return this.each(function(){const t=Ni.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}})}}yt.on(window,Ti,()=>{for(const e of Et.find('[data-bs-spy="scroll"]'))Ni.getOrCreateInstance(e)}),Ze(Ni);const Fi=".bs.tab",Mi=`hide${Fi}`,$i=`hidden${Fi}`,qi=`show${Fi}`,Hi=`shown${Fi}`,Ri=`click${Fi}`,Bi=`keydown${Fi}`,Wi=`load${Fi}`,zi="ArrowLeft",Vi="ArrowRight",Ui="ArrowUp",Xi="ArrowDown",Ki="Home",Gi="End",Qi="active",Yi="fade",Ji="show",Zi=".dropdown-toggle",eo=`:not(${Zi})`,to='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',no=`${`.nav-link${eo}, .list-group-item${eo}, [role="tab"]${eo}`}, ${to}`,ro=`.${Qi}[data-bs-toggle="tab"], .${Qi}[data-bs-toggle="pill"], .${Qi}[data-bs-toggle="list"]`;class io extends At{constructor(e){super(e),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),yt.on(this._element,Bi,e=>this._keydown(e)))}static get NAME(){return"tab"}show(){const e=this._element;if(this._elemIsActive(e))return;const t=this._getActiveElem(),n=t?yt.trigger(t,Mi,{relatedTarget:e}):null;yt.trigger(e,qi,{relatedTarget:t}).defaultPrevented||n&&n.defaultPrevented||(this._deactivate(t,e),this._activate(e,t))}_activate(e,t){if(!e)return;e.classList.add(Qi),this._activate(Et.getElementFromSelector(e));this._queueCallback(()=>{"tab"===e.getAttribute("role")?(e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),this._toggleDropDown(e,!0),yt.trigger(e,Hi,{relatedTarget:t})):e.classList.add(Ji)},e,e.classList.contains(Yi))}_deactivate(e,t){if(!e)return;e.classList.remove(Qi),e.blur(),this._deactivate(Et.getElementFromSelector(e));this._queueCallback(()=>{"tab"===e.getAttribute("role")?(e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),this._toggleDropDown(e,!1),yt.trigger(e,$i,{relatedTarget:t})):e.classList.remove(Ji)},e,e.classList.contains(Yi))}_keydown(e){if(![zi,Vi,Ui,Xi,Ki,Gi].includes(e.key))return;e.stopPropagation(),e.preventDefault();const t=this._getChildren().filter(e=>!Ue(e));let n;if([Ki,Gi].includes(e.key))n=t[e.key===Ki?0:t.length-1];else{const r=[Vi,Xi].includes(e.key);n=nt(t,e.target,r,!0)}n&&(n.focus({preventScroll:!0}),io.getOrCreateInstance(n).show())}_getChildren(){return Et.find(no,this._parent)}_getActiveElem(){return this._getChildren().find(e=>this._elemIsActive(e))||null}_setInitialAttributes(e,t){this._setAttributeIfNotExists(e,"role","tablist");for(const e of t)this._setInitialAttributesOnChild(e)}_setInitialAttributesOnChild(e){e=this._getInnerElement(e);const t=this._elemIsActive(e),n=this._getOuterElement(e);e.setAttribute("aria-selected",t),n!==e&&this._setAttributeIfNotExists(n,"role","presentation"),t||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}_setInitialAttributesOnTargetPanel(e){const t=Et.getElementFromSelector(e);t&&(this._setAttributeIfNotExists(t,"role","tabpanel"),e.id&&this._setAttributeIfNotExists(t,"aria-labelledby",`${e.id}`))}_toggleDropDown(e,t){const n=this._getOuterElement(e);if(!n.classList.contains("dropdown"))return;const r=(e,r)=>{const i=Et.findOne(e,n);i&&i.classList.toggle(r,t)};r(Zi,Qi),r(".dropdown-menu",Ji),n.setAttribute("aria-expanded",t)}_setAttributeIfNotExists(e,t,n){e.hasAttribute(t)||e.setAttribute(t,n)}_elemIsActive(e){return e.classList.contains(Qi)}_getInnerElement(e){return e.matches(no)?e:Et.findOne(no,e)}_getOuterElement(e){return e.closest(".nav-item, .list-group-item")||e}static jQueryInterface(e){return this.each(function(){const t=io.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}})}}yt.on(document,Ri,to,function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),Ue(this)||io.getOrCreateInstance(this).show()}),yt.on(window,Wi,()=>{for(const e of Et.find(ro))io.getOrCreateInstance(e)}),Ze(io);const oo=".bs.toast",so=`mouseover${oo}`,ao=`mouseout${oo}`,lo=`focusin${oo}`,co=`focusout${oo}`,uo=`hide${oo}`,po=`hidden${oo}`,fo=`show${oo}`,ho=`shown${oo}`,go="hide",mo="show",vo="showing",yo={animation:"boolean",autohide:"boolean",delay:"number"},bo={animation:!0,autohide:!0,delay:5e3};class _o extends At{constructor(e,t){super(e,t),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return bo}static get DefaultType(){return yo}static get NAME(){return"toast"}show(){if(yt.trigger(this._element,fo).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add("fade");this._element.classList.remove(go),Ge(this._element),this._element.classList.add(mo,vo),this._queueCallback(()=>{this._element.classList.remove(vo),yt.trigger(this._element,ho),this._maybeScheduleHide()},this._element,this._config.animation)}hide(){if(!this.isShown())return;if(yt.trigger(this._element,uo).defaultPrevented)return;this._element.classList.add(vo),this._queueCallback(()=>{this._element.classList.add(go),this._element.classList.remove(vo,mo),yt.trigger(this._element,po)},this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(mo),super.dispose()}isShown(){return this._element.classList.contains(mo)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}if(t)return void this._clearTimeout();const n=e.relatedTarget;this._element===n||this._element.contains(n)||this._maybeScheduleHide()}_setListeners(){yt.on(this._element,so,e=>this._onInteraction(e,!0)),yt.on(this._element,ao,e=>this._onInteraction(e,!1)),yt.on(this._element,lo,e=>this._onInteraction(e,!0)),yt.on(this._element,co,e=>this._onInteraction(e,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each(function(){const t=_o.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e](this)}})}}function wo(e,t){e.split(/\s+/).forEach(e=>{t(e)})}Ct(_o),Ze(_o);class xo{constructor(){this._events={}}on(e,t){wo(e,e=>{const n=this._events[e]||[];n.push(t),this._events[e]=n})}off(e,t){var n=arguments.length;0!==n?wo(e,e=>{if(1===n)return void delete this._events[e];const r=this._events[e];void 0!==r&&(r.splice(r.indexOf(t),1),this._events[e]=r)}):this._events={}}trigger(e,...t){var n=this;wo(e,e=>{const r=n._events[e];void 0!==r&&r.forEach(e=>{e.apply(n,t)})})}}const Oo=e=>(e=e.filter(Boolean)).length<2?e[0]||"":1==To(e)?"["+e.join("")+"]":"(?:"+e.join("|")+")",Ao=e=>{if(!Eo(e))return e.join("");let t="",n=0;const r=()=>{n>1&&(t+="{"+n+"}")};return e.forEach((i,o)=>{i!==e[o-1]?(r(),t+=i,n=1):n++}),r(),t},So=e=>{let t=Array.from(e);return Oo(t)},Eo=e=>new Set(e).size!==e.length,Co=e=>(e+"").replace(/([\$\(\)\*\+\.\?\[\]\^\{\|\}\\])/gu,"\\$1"),To=e=>e.reduce((e,t)=>Math.max(e,ko(t)),0),ko=e=>Array.from(e).length,Lo=e=>{if(1===e.length)return[[e]];let t=[];const n=e.substring(1);return Lo(n).forEach(function(n){let r=n.slice(0);r[0]=e.charAt(0)+r[0],t.push(r),r=n.slice(0),r.unshift(e.charAt(0)),t.push(r)}),t},jo=[[0,65535]];let Io,Do;const Po={},No={"/":"⁄∕",0:"߀",a:"ⱥɐɑ",aa:"ꜳ",ae:"æǽǣ",ao:"ꜵ",au:"ꜷ",av:"ꜹꜻ",ay:"ꜽ",b:"ƀɓƃ",c:"ꜿƈȼↄ",d:"đɗɖᴅƌꮷԁɦ",e:"ɛǝᴇɇ",f:"ꝼƒ",g:"ǥɠꞡᵹꝿɢ",h:"ħⱨⱶɥ",i:"ɨı",j:"ɉȷ",k:"ƙⱪꝁꝃꝅꞣ",l:"łƚɫⱡꝉꝇꞁɭ",m:"ɱɯϻ",n:"ꞥƞɲꞑᴎлԉ",o:"øǿɔɵꝋꝍᴑ",oe:"œ",oi:"ƣ",oo:"ꝏ",ou:"ȣ",p:"ƥᵽꝑꝓꝕρ",q:"ꝗꝙɋ",r:"ɍɽꝛꞧꞃ",s:"ßȿꞩꞅʂ",t:"ŧƭʈⱦꞇ",th:"þ",tz:"ꜩ",u:"ʉ",v:"ʋꝟʌ",vy:"ꝡ",w:"ⱳ",y:"ƴɏỿ",z:"ƶȥɀⱬꝣ",hv:"ƕ"};for(let e in No){let t=No[e]||"";for(let n=0;n<t.length;n++){let r=t.substring(n,n+1);Po[r]=e}}const Fo=new RegExp(Object.keys(Po).join("|")+"|[̀-ͯ·ʾʼ]","gu"),Mo=(e,t="NFKD")=>e.normalize(t),$o=e=>Array.from(e).reduce((e,t)=>e+qo(t),""),qo=e=>(e=Mo(e).toLowerCase().replace(Fo,e=>Po[e]||""),Mo(e,"NFC"));const Ho=e=>{const t={},n=(e,n)=>{const r=t[e]||new Set,i=new RegExp("^"+So(r)+"$","iu");n.match(i)||(r.add(Co(n)),t[e]=r)};for(let t of function*(e){for(const[t,n]of e)for(let e=t;e<=n;e++){let t=String.fromCharCode(e),n=$o(t);n!=t.toLowerCase()&&(n.length>3||0!=n.length&&(yield{folded:n,composed:t,code_point:e}))}}(e))n(t.folded,t.folded),n(t.folded,t.composed);return t},Ro=e=>{const t=Ho(e),n={};let r=[];for(let e in t){let i=t[e];i&&(n[e]=So(i)),e.length>1&&r.push(Co(e))}r.sort((e,t)=>t.length-e.length);const i=Oo(r);return Do=new RegExp("^"+i,"u"),n},Bo=(e,t=1)=>(t=Math.max(t,e.length-1),Oo(Lo(e).map(e=>((e,t=1)=>{let n=0;return e=e.map(e=>(Io[e]&&(n+=e.length),Io[e]||e)),n>=t?Ao(e):""})(e,t)))),Wo=(e,t=!0)=>{let n=e.length>1?1:0;return Oo(e.map(e=>{let r=[];const i=t?e.length():e.length()-1;for(let t=0;t<i;t++)r.push(Bo(e.substrs[t]||"",n));return Ao(r)}))},zo=(e,t)=>{for(const n of t){if(n.start!=e.start||n.end!=e.end)continue;if(n.substrs.join("")!==e.substrs.join(""))continue;let t=e.parts;const r=e=>{for(const n of t){if(n.start===e.start&&n.substr===e.substr)return!1;if(1!=e.length&&1!=n.length){if(e.start<n.start&&e.end>n.start)return!0;if(n.start<e.start&&n.end>e.start)return!0}}return!1};if(!(n.parts.filter(r).length>0))return!0}return!1};class Vo{parts;substrs;start;end;constructor(){this.parts=[],this.substrs=[],this.start=0,this.end=0}add(e){e&&(this.parts.push(e),this.substrs.push(e.substr),this.start=Math.min(e.start,this.start),this.end=Math.max(e.end,this.end))}last(){return this.parts[this.parts.length-1]}length(){return this.parts.length}clone(e,t){let n=new Vo,r=JSON.parse(JSON.stringify(this.parts)),i=r.pop();for(const e of r)n.add(e);let o=t.substr.substring(0,e-i.start),s=o.length;return n.add({start:i.start,end:i.start+s,length:s,substr:o}),n}}const Uo=e=>{var t;void 0===Io&&(Io=Ro(t||jo)),e=$o(e);let n="",r=[new Vo];for(let t=0;t<e.length;t++){let i=e.substring(t).match(Do);const o=e.substring(t,t+1),s=i?i[0]:null;let a=[],l=new Set;for(const e of r){const n=e.last();if(!n||1==n.length||n.end<=t)if(s){const n=s.length;e.add({start:t,end:t+n,length:n,substr:s}),l.add("1")}else e.add({start:t,end:t+1,length:1,substr:o}),l.add("2");else if(s){let r=e.clone(t,n);const i=s.length;r.add({start:t,end:t+i,length:i,substr:s}),a.push(r)}else l.add("3")}if(a.length>0){a=a.sort((e,t)=>e.length()-t.length());for(let e of a)zo(e,r)||r.push(e)}else if(t>0&&1==l.size&&!l.has("3")){n+=Wo(r,!1);let e=new Vo;const t=r[0];t&&e.add(t.last()),r=[e]}}return n+=Wo(r,!0),n},Xo=(e,t)=>{if(e)return e[t]},Ko=(e,t)=>{if(e){for(var n,r=t.split(".");(n=r.shift())&&(e=e[n]););return e}},Go=(e,t,n)=>{var r,i;return e?(e+="",null==t.regex||-1===(i=e.search(t.regex))?0:(r=t.string.length/e.length,0===i&&(r+=.5),r*n)):0},Qo=(e,t)=>{var n=e[t];if("function"==typeof n)return n;n&&!Array.isArray(n)&&(e[t]=[n])},Yo=(e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)},Jo=(e,t)=>"number"==typeof e&&"number"==typeof t?e>t?1:e<t?-1:0:(e=$o(e+"").toLowerCase())>(t=$o(t+"").toLowerCase())?1:t>e?-1:0;class Zo{items;settings;constructor(e,t){this.items=e,this.settings=t||{diacritics:!0}}tokenize(e,t,n){if(!e||!e.length)return[];const r=[],i=e.split(/\s+/);var o;return n&&(o=new RegExp("^("+Object.keys(n).map(Co).join("|")+"):(.*)$")),i.forEach(e=>{let n,i=null,s=null;o&&(n=e.match(o))&&(i=n[1],e=n[2]),e.length>0&&(s=this.settings.diacritics?Uo(e)||null:Co(e),s&&t&&(s="\\b"+s)),r.push({string:e,regex:s?new RegExp(s,"iu"):null,field:i})}),r}getScoreFunction(e,t){var n=this.prepareSearch(e,t);return this._getScoreFunction(n)}_getScoreFunction(e){const t=e.tokens,n=t.length;if(!n)return function(){return 0};const r=e.options.fields,i=e.weights,o=r.length,s=e.getAttrFn;if(!o)return function(){return 1};const a=1===o?function(e,t){const n=r[0].field;return Go(s(t,n),e,i[n]||1)}:function(e,t){var n=0;if(e.field){const r=s(t,e.field);!e.regex&&r?n+=1/o:n+=Go(r,e,1)}else Yo(i,(r,i)=>{n+=Go(s(t,i),e,r)});return n/o};return 1===n?function(e){return a(t[0],e)}:"and"===e.options.conjunction?function(e){var r,i=0;for(let n of t){if((r=a(n,e))<=0)return 0;i+=r}return i/n}:function(e){var r=0;return Yo(t,t=>{r+=a(t,e)}),r/n}}getSortFunction(e,t){var n=this.prepareSearch(e,t);return this._getSortFunction(n)}_getSortFunction(e){var t,n=[];const r=this,i=e.options,o=!e.query&&i.sort_empty?i.sort_empty:i.sort;if("function"==typeof o)return o.bind(this);const s=function(t,n){return"$score"===t?n.score:e.getAttrFn(r.items[n.id],t)};if(o)for(let t of o)(e.query||"$score"!==t.field)&&n.push(t);if(e.query){t=!0;for(let e of n)if("$score"===e.field){t=!1;break}t&&n.unshift({field:"$score",direction:"desc"})}else n=n.filter(e=>"$score"!==e.field);return n.length?function(e,t){var r,i;for(let o of n){if(i=o.field,r=("desc"===o.direction?-1:1)*Jo(s(i,e),s(i,t)))return r}return 0}:null}prepareSearch(e,t){const n={};var r=Object.assign({},t);if(Qo(r,"sort"),Qo(r,"sort_empty"),r.fields){Qo(r,"fields");const e=[];r.fields.forEach(t=>{"string"==typeof t&&(t={field:t,weight:1}),e.push(t),n[t.field]="weight"in t?t.weight:1}),r.fields=e}return{options:r,query:e.toLowerCase().trim(),tokens:this.tokenize(e,r.respect_word_boundaries,n),total:0,items:[],weights:n,getAttrFn:r.nesting?Ko:Xo}}search(e,t){var n,r,i=this;r=this.prepareSearch(e,t),t=r.options,e=r.query;const o=t.score||i._getScoreFunction(r);e.length?Yo(i.items,(e,i)=>{n=o(e),(!1===t.filter||n>0)&&r.items.push({score:n,id:i})}):Yo(i.items,(e,t)=>{r.items.push({score:1,id:t})});const s=i._getSortFunction(r);return s&&r.items.sort(s),r.total=r.items.length,"number"==typeof t.limit&&(r.items=r.items.slice(0,t.limit)),r}}const es=e=>null==e?null:ts(e),ts=e=>"boolean"==typeof e?e?"1":"0":e+"",ns=e=>(e+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"),rs=(e,t)=>{var n;return function(r,i){var o=this;n&&(o.loading=Math.max(o.loading-1,0),clearTimeout(n)),n=setTimeout(function(){n=null,o.loadedSearches[r]=!0,e.call(o,r,i)},t)}},is=(e,t,n)=>{var r,i=e.trigger,o={};for(r of(e.trigger=function(){var n=arguments[0];if(-1===t.indexOf(n))return i.apply(e,arguments);o[n]=arguments},n.apply(e,[]),e.trigger=i,t))r in o&&i.apply(e,o[r])},os=(e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())},ss=(e,t,n,r)=>{e.addEventListener(t,n,r)},as=(e,t)=>!!t&&(!!t[e]&&1===(t.altKey?1:0)+(t.ctrlKey?1:0)+(t.shiftKey?1:0)+(t.metaKey?1:0)),ls=(e,t)=>{const n=e.getAttribute("id");return n||(e.setAttribute("id",t),t)},cs=e=>e.replace(/[\\"']/g,"\\$&"),us=(e,t)=>{t&&e.append(t)},ds=(e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)},ps=e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if(fs(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)},fs=e=>"string"==typeof e&&e.indexOf("<")>-1,hs=(e,t)=>{var n=document.createEvent("HTMLEvents");n.initEvent(t,!0,!1),e.dispatchEvent(n)},gs=(e,t)=>{Object.assign(e.style,t)},ms=(e,...t)=>{var n=ys(t);(e=bs(e)).map(e=>{n.map(t=>{e.classList.add(t)})})},vs=(e,...t)=>{var n=ys(t);(e=bs(e)).map(e=>{n.map(t=>{e.classList.remove(t)})})},ys=e=>{var t=[];return ds(e,e=>{"string"==typeof e&&(e=e.trim().split(/[\t\n\f\r\s]/)),Array.isArray(e)&&(t=t.concat(e))}),t.filter(Boolean)},bs=e=>(Array.isArray(e)||(e=[e]),e),_s=(e,t,n)=>{if(!n||n.contains(e))for(;e&&e.matches;){if(e.matches(t))return e;e=e.parentNode}},ws=(e,t=0)=>t>0?e[e.length-1]:e[0],xs=(e,t)=>{if(!e)return-1;t=t||e.nodeName;for(var n=0;e=e.previousElementSibling;)e.matches(t)&&n++;return n},Os=(e,t)=>{ds(t,(t,n)=>{null==t?e.removeAttribute(n):e.setAttribute(n,""+t)})},As=(e,t)=>{e.parentNode&&e.parentNode.replaceChild(t,e)},Ss=(e,t)=>{if(null===t)return;if("string"==typeof t){if(!t.length)return;t=new RegExp(t,"i")}const n=e=>3===e.nodeType?(e=>{var n=e.data.match(t);if(n&&e.data.length>0){var r=document.createElement("span");r.className="highlight";var i=e.splitText(n.index);i.splitText(n[0].length);var o=i.cloneNode(!0);return r.appendChild(o),As(i,r),1}return 0})(e):((e=>{1!==e.nodeType||!e.childNodes||/(script|style)/i.test(e.tagName)||"highlight"===e.className&&"SPAN"===e.tagName||Array.from(e.childNodes).forEach(e=>{n(e)})})(e),0);n(e)},Es="undefined"!=typeof navigator&&/Mac/.test(navigator.userAgent)?"metaKey":"ctrlKey",Cs={options:[],optgroups:[],plugins:[],delimiter:",",splitOn:null,persist:!0,diacritics:!0,create:null,createOnBlur:!1,createFilter:null,highlight:!0,openOnFocus:!0,shouldOpen:null,maxOptions:50,maxItems:null,hideSelected:null,duplicates:!1,addPrecedence:!1,selectOnTab:!1,preload:null,allowEmptyOption:!1,refreshThrottle:300,loadThrottle:300,loadingClass:"loading",dataAttr:null,optgroupField:"optgroup",valueField:"value",labelField:"text",disabledField:"disabled",optgroupLabelField:"label",optgroupValueField:"value",lockOptgroupOrder:!1,sortField:"$order",searchField:["text"],searchConjunction:"and",mode:null,wrapperClass:"ts-wrapper",controlClass:"ts-control",dropdownClass:"ts-dropdown",dropdownContentClass:"ts-dropdown-content",itemClass:"item",optionClass:"option",dropdownParent:null,controlInput:'<input type="text" autocomplete="off" size="1" />',copyClassesToDropdown:!1,placeholder:null,hidePlaceholder:null,shouldLoad:function(e){return e.length>0},render:{}};function Ts(e,t){var n=Object.assign({},Cs,t),r=n.dataAttr,i=n.labelField,o=n.valueField,s=n.disabledField,a=n.optgroupField,l=n.optgroupLabelField,c=n.optgroupValueField,u=e.tagName.toLowerCase(),d=e.getAttribute("placeholder")||e.getAttribute("data-placeholder");if(!d&&!n.allowEmptyOption){let t=e.querySelector('option[value=""]');t&&(d=t.textContent)}var p={placeholder:d,options:[],optgroups:[],items:[],maxItems:null};return"select"===u?(()=>{var t,u=p.options,d={},f=1;let h=0;var g=e=>{var t=Object.assign({},e.dataset),n=r&&t[r];return"string"==typeof n&&n.length&&(t=Object.assign(t,JSON.parse(n))),t},m=(e,t)=>{var r=es(e.value);if(null!=r&&(r||n.allowEmptyOption)){if(d.hasOwnProperty(r)){if(t){var l=d[r][a];l?Array.isArray(l)?l.push(t):d[r][a]=[l,t]:d[r][a]=t}}else{var c=g(e);c[i]=c[i]||e.textContent,c[o]=c[o]||r,c[s]=c[s]||e.disabled,c[a]=c[a]||t,c.$option=e,c.$order=c.$order||++h,d[r]=c,u.push(c)}e.selected&&p.items.push(r)}};p.maxItems=e.hasAttribute("multiple")?null:1,ds(e.children,e=>{var n,r,i;"optgroup"===(t=e.tagName.toLowerCase())?((i=g(n=e))[l]=i[l]||n.getAttribute("label")||"",i[c]=i[c]||f++,i[s]=i[s]||n.disabled,i.$order=i.$order||++h,p.optgroups.push(i),r=i[c],ds(n.children,e=>{m(e,r)})):"option"===t&&m(e)})})():(()=>{const t=e.getAttribute(r);if(t)p.options=JSON.parse(t),ds(p.options,e=>{p.items.push(e[o])});else{var s=e.value.trim()||"";if(!n.allowEmptyOption&&!s.length)return;const t=s.split(n.delimiter);ds(t,e=>{const t={};t[i]=e,t[o]=e,p.options.push(t)}),p.items=t}})(),Object.assign({},Cs,p,t)}var ks=0;class Ls extends(function(e){return e.plugins={},class extends e{constructor(){super(...arguments),this.plugins={names:[],settings:{},requested:{},loaded:{}}}static define(t,n){e.plugins[t]={name:t,fn:n}}initializePlugins(e){var t,n;const r=this,i=[];if(Array.isArray(e))e.forEach(e=>{"string"==typeof e?i.push(e):(r.plugins.settings[e.name]=e.options,i.push(e.name))});else if(e)for(t in e)e.hasOwnProperty(t)&&(r.plugins.settings[t]=e[t],i.push(t));for(;n=i.shift();)r.require(n)}loadPlugin(t){var n=this,r=n.plugins,i=e.plugins[t];if(!e.plugins.hasOwnProperty(t))throw new Error('Unable to find "'+t+'" plugin');r.requested[t]=!0,r.loaded[t]=i.fn.apply(n,[n.plugins.settings[t]||{}]),r.names.push(t)}require(e){var t=this,n=t.plugins;if(!t.plugins.loaded.hasOwnProperty(e)){if(n.requested[e])throw new Error('Plugin has circular dependency ("'+e+'")');t.loadPlugin(e)}return n.loaded[e]}}}(xo)){constructor(e,t){var n;super(),this.order=0,this.isOpen=!1,this.isDisabled=!1,this.isReadOnly=!1,this.isInvalid=!1,this.isValid=!0,this.isLocked=!1,this.isFocused=!1,this.isInputHidden=!1,this.isSetup=!1,this.ignoreFocus=!1,this.ignoreHover=!1,this.hasOptions=!1,this.lastValue="",this.caretPos=0,this.loading=0,this.loadedSearches={},this.activeOption=null,this.activeItems=[],this.optgroups={},this.options={},this.userOptions={},this.items=[],this.refreshTimeout=null,ks++;var r=ps(e);if(r.tomselect)throw new Error("Tom Select already initialized on this element");r.tomselect=this,n=(window.getComputedStyle&&window.getComputedStyle(r,null)).getPropertyValue("direction");const i=Ts(r,t);this.settings=i,this.input=r,this.tabIndex=r.tabIndex||0,this.is_select_tag="select"===r.tagName.toLowerCase(),this.rtl=/rtl/i.test(n),this.inputId=ls(r,"tomselect-"+ks),this.isRequired=r.required,this.sifter=new Zo(this.options,{diacritics:i.diacritics}),i.mode=i.mode||(1===i.maxItems?"single":"multi"),"boolean"!=typeof i.hideSelected&&(i.hideSelected="multi"===i.mode),"boolean"!=typeof i.hidePlaceholder&&(i.hidePlaceholder="multi"!==i.mode);var o=i.createFilter;"function"!=typeof o&&("string"==typeof o&&(o=new RegExp(o)),o instanceof RegExp?i.createFilter=e=>o.test(e):i.createFilter=e=>this.settings.duplicates||!this.options[e]),this.initializePlugins(i.plugins),this.setupCallbacks(),this.setupTemplates();const s=ps("<div>"),a=ps("<div>"),l=this._render("dropdown"),c=ps('<div role="listbox" tabindex="-1">'),u=this.input.getAttribute("class")||"",d=i.mode;var p;if(ms(s,i.wrapperClass,u,d),ms(a,i.controlClass),us(s,a),ms(l,i.dropdownClass,d),i.copyClassesToDropdown&&ms(l,u),ms(c,i.dropdownContentClass),us(l,c),ps(i.dropdownParent||s).appendChild(l),fs(i.controlInput)){p=ps(i.controlInput);ds(["autocorrect","autocapitalize","autocomplete","spellcheck"],e=>{r.getAttribute(e)&&Os(p,{[e]:r.getAttribute(e)})}),p.tabIndex=-1,a.appendChild(p),this.focus_node=p}else i.controlInput?(p=ps(i.controlInput),this.focus_node=p):(p=ps("<input/>"),this.focus_node=a);this.wrapper=s,this.dropdown=l,this.dropdown_content=c,this.control=a,this.control_input=p,this.setup()}setup(){const e=this,t=e.settings,n=e.control_input,r=e.dropdown,i=e.dropdown_content,o=e.wrapper,s=e.control,a=e.input,l=e.focus_node,c={passive:!0},u=e.inputId+"-ts-dropdown";Os(i,{id:u}),Os(l,{role:"combobox","aria-haspopup":"listbox","aria-expanded":"false","aria-controls":u});const d=ls(l,e.inputId+"-ts-control"),p="label[for='"+(e=>e.replace(/['"\\]/g,"\\$&"))(e.inputId)+"']",f=document.querySelector(p),h=e.focus.bind(e);if(f){ss(f,"click",h),Os(f,{for:d});const t=ls(f,e.inputId+"-ts-label");Os(l,{"aria-labelledby":t}),Os(i,{"aria-labelledby":t})}if(o.style.width=a.style.width,e.plugins.names.length){const t="plugin-"+e.plugins.names.join(" plugin-");ms([o,r],t)}(null===t.maxItems||t.maxItems>1)&&e.is_select_tag&&Os(a,{multiple:"multiple"}),t.placeholder&&Os(n,{placeholder:t.placeholder}),!t.splitOn&&t.delimiter&&(t.splitOn=new RegExp("\\s*"+Co(t.delimiter)+"+\\s*")),t.load&&t.loadThrottle&&(t.load=rs(t.load,t.loadThrottle)),ss(r,"mousemove",()=>{e.ignoreHover=!1}),ss(r,"mouseenter",t=>{var n=_s(t.target,"[data-selectable]",r);n&&e.onOptionHover(t,n)},{capture:!0}),ss(r,"click",t=>{const n=_s(t.target,"[data-selectable]");n&&(e.onOptionSelect(t,n),os(t,!0))}),ss(s,"click",t=>{var r=_s(t.target,"[data-ts-item]",s);r&&e.onItemSelect(t,r)?os(t,!0):""==n.value&&(e.onClick(),os(t,!0))}),ss(l,"keydown",t=>e.onKeyDown(t)),ss(n,"keypress",t=>e.onKeyPress(t)),ss(n,"input",t=>e.onInput(t)),ss(l,"blur",t=>e.onBlur(t)),ss(l,"focus",t=>e.onFocus(t)),ss(n,"paste",t=>e.onPaste(t));const g=t=>{const i=t.composedPath()[0];if(!o.contains(i)&&!r.contains(i))return e.isFocused&&e.blur(),void e.inputState();i==n&&e.isOpen?t.stopPropagation():os(t,!0)},m=()=>{e.isOpen&&e.positionDropdown()};ss(document,"mousedown",g),ss(window,"scroll",m,c),ss(window,"resize",m,c),this._destroy=()=>{document.removeEventListener("mousedown",g),window.removeEventListener("scroll",m),window.removeEventListener("resize",m),f&&f.removeEventListener("click",h)},this.revertSettings={innerHTML:a.innerHTML,tabIndex:a.tabIndex},a.tabIndex=-1,a.insertAdjacentElement("afterend",e.wrapper),e.sync(!1),t.items=[],delete t.optgroups,delete t.options,ss(a,"invalid",()=>{e.isValid&&(e.isValid=!1,e.isInvalid=!0,e.refreshState())}),e.updateOriginalInput(),e.refreshItems(),e.close(!1),e.inputState(),e.isSetup=!0,a.disabled?e.disable():a.readOnly?e.setReadOnly(!0):e.enable(),e.on("change",this.onChange),ms(a,"tomselected","ts-hidden-accessible"),e.trigger("initialize"),!0===t.preload&&e.preload()}setupOptions(e=[],t=[]){this.addOptions(e),ds(t,e=>{this.registerOptionGroup(e)})}setupTemplates(){var e=this,t=e.settings.labelField,n=e.settings.optgroupLabelField,r={optgroup:e=>{let t=document.createElement("div");return t.className="optgroup",t.appendChild(e.options),t},optgroup_header:(e,t)=>'<div class="optgroup-header">'+t(e[n])+"</div>",option:(e,n)=>"<div>"+n(e[t])+"</div>",item:(e,n)=>"<div>"+n(e[t])+"</div>",option_create:(e,t)=>'<div class="create">Add <strong>'+t(e.input)+"</strong>&hellip;</div>",no_results:()=>'<div class="no-results">No results found</div>',loading:()=>'<div class="spinner"></div>',not_loading:()=>{},dropdown:()=>"<div></div>"};e.settings.render=Object.assign({},r,e.settings.render)}setupCallbacks(){var e,t,n={initialize:"onInitialize",change:"onChange",item_add:"onItemAdd",item_remove:"onItemRemove",item_select:"onItemSelect",clear:"onClear",option_add:"onOptionAdd",option_remove:"onOptionRemove",option_clear:"onOptionClear",optgroup_add:"onOptionGroupAdd",optgroup_remove:"onOptionGroupRemove",optgroup_clear:"onOptionGroupClear",dropdown_open:"onDropdownOpen",dropdown_close:"onDropdownClose",type:"onType",load:"onLoad",focus:"onFocus",blur:"onBlur"};for(e in n)(t=this.settings[n[e]])&&this.on(e,t)}sync(e=!0){const t=this,n=e?Ts(t.input,{delimiter:t.settings.delimiter}):t.settings;t.setupOptions(n.options,n.optgroups),t.setValue(n.items||[],!0),t.lastQuery=null}onClick(){var e=this;if(e.activeItems.length>0)return e.clearActiveItems(),void e.focus();e.isFocused&&e.isOpen?e.blur():e.focus()}onMouseDown(){}onChange(){hs(this.input,"input"),hs(this.input,"change")}onPaste(e){var t=this;t.isInputHidden||t.isLocked?os(e):t.settings.splitOn&&setTimeout(()=>{var e=t.inputValue();if(e.match(t.settings.splitOn)){var n=e.trim().split(t.settings.splitOn);ds(n,e=>{es(e)&&(this.options[e]?t.addItem(e):t.createItem(e))})}},0)}onKeyPress(e){var t=this;if(!t.isLocked){var n=String.fromCharCode(e.keyCode||e.which);return t.settings.create&&"multi"===t.settings.mode&&n===t.settings.delimiter?(t.createItem(),void os(e)):void 0}os(e)}onKeyDown(e){var t=this;if(t.ignoreHover=!0,t.isLocked)9!==e.keyCode&&os(e);else{switch(e.keyCode){case 65:if(as(Es,e)&&""==t.control_input.value)return os(e),void t.selectAll();break;case 27:return t.isOpen&&(os(e,!0),t.close()),void t.clearActiveItems();case 40:if(!t.isOpen&&t.hasOptions)t.open();else if(t.activeOption){let e=t.getAdjacent(t.activeOption,1);e&&t.setActiveOption(e)}return void os(e);case 38:if(t.activeOption){let e=t.getAdjacent(t.activeOption,-1);e&&t.setActiveOption(e)}return void os(e);case 13:return void(t.canSelect(t.activeOption)?(t.onOptionSelect(e,t.activeOption),os(e)):(t.settings.create&&t.createItem()||document.activeElement==t.control_input&&t.isOpen)&&os(e));case 37:return void t.advanceSelection(-1,e);case 39:return void t.advanceSelection(1,e);case 9:return void(t.settings.selectOnTab&&(t.canSelect(t.activeOption)&&(t.onOptionSelect(e,t.activeOption),os(e)),t.settings.create&&t.createItem()&&os(e)));case 8:case 46:return void t.deleteSelection(e)}t.isInputHidden&&!as(Es,e)&&os(e)}}onInput(e){if(this.isLocked)return;const t=this.inputValue();this.lastValue!==t&&(this.lastValue=t,""!=t?(this.refreshTimeout&&window.clearTimeout(this.refreshTimeout),this.refreshTimeout=((e,t)=>t>0?window.setTimeout(e,t):(e.call(null),null))(()=>{this.refreshTimeout=null,this._onInput()},this.settings.refreshThrottle)):this._onInput())}_onInput(){const e=this.lastValue;this.settings.shouldLoad.call(this,e)&&this.load(e),this.refreshOptions(),this.trigger("type",e)}onOptionHover(e,t){this.ignoreHover||this.setActiveOption(t,!1)}onFocus(e){var t=this,n=t.isFocused;if(t.isDisabled||t.isReadOnly)return t.blur(),void os(e);t.ignoreFocus||(t.isFocused=!0,"focus"===t.settings.preload&&t.preload(),n||t.trigger("focus"),t.activeItems.length||(t.inputState(),t.refreshOptions(!!t.settings.openOnFocus)),t.refreshState())}onBlur(e){if(!1!==document.hasFocus()){var t=this;if(t.isFocused){t.isFocused=!1,t.ignoreFocus=!1;var n=()=>{t.close(),t.setActiveItem(),t.setCaret(t.items.length),t.trigger("blur")};t.settings.create&&t.settings.createOnBlur?t.createItem(null,n):n()}}}onOptionSelect(e,t){var n,r=this;t.parentElement&&t.parentElement.matches("[data-disabled]")||(t.classList.contains("create")?r.createItem(null,()=>{r.settings.closeAfterSelect&&r.close()}):void 0!==(n=t.dataset.value)&&(r.lastQuery=null,r.addItem(n),r.settings.closeAfterSelect&&r.close(),!r.settings.hideSelected&&e.type&&/click/.test(e.type)&&r.setActiveOption(t)))}canSelect(e){return!!(this.isOpen&&e&&this.dropdown_content.contains(e))}onItemSelect(e,t){var n=this;return!n.isLocked&&"multi"===n.settings.mode&&(os(e),n.setActiveItem(t,e),!0)}canLoad(e){return!!this.settings.load&&!this.loadedSearches.hasOwnProperty(e)}load(e){const t=this;if(!t.canLoad(e))return;ms(t.wrapper,t.settings.loadingClass),t.loading++;const n=t.loadCallback.bind(t);t.settings.load.call(t,e,n)}loadCallback(e,t){const n=this;n.loading=Math.max(n.loading-1,0),n.lastQuery=null,n.clearActiveOption(),n.setupOptions(e,t),n.refreshOptions(n.isFocused&&!n.isInputHidden),n.loading||vs(n.wrapper,n.settings.loadingClass),n.trigger("load",e,t)}preload(){var e=this.wrapper.classList;e.contains("preloaded")||(e.add("preloaded"),this.load(""))}setTextboxValue(e=""){var t=this.control_input;t.value!==e&&(t.value=e,hs(t,"update"),this.lastValue=e)}getValue(){return this.is_select_tag&&this.input.hasAttribute("multiple")?this.items:this.items.join(this.settings.delimiter)}setValue(e,t){is(this,t?[]:["change"],()=>{this.clear(t),this.addItems(e,t)})}setMaxItems(e){0===e&&(e=null),this.settings.maxItems=e,this.refreshState()}setActiveItem(e,t){var n,r,i,o,s,a,l=this;if("single"!==l.settings.mode){if(!e)return l.clearActiveItems(),void(l.isFocused&&l.inputState());if("click"===(n=t&&t.type.toLowerCase())&&as("shiftKey",t)&&l.activeItems.length){for(a=l.getLastActive(),(i=Array.prototype.indexOf.call(l.control.children,a))>(o=Array.prototype.indexOf.call(l.control.children,e))&&(s=i,i=o,o=s),r=i;r<=o;r++)e=l.control.children[r],-1===l.activeItems.indexOf(e)&&l.setActiveItemClass(e);os(t)}else"click"===n&&as(Es,t)||"keydown"===n&&as("shiftKey",t)?e.classList.contains("active")?l.removeActiveItem(e):l.setActiveItemClass(e):(l.clearActiveItems(),l.setActiveItemClass(e));l.inputState(),l.isFocused||l.focus()}}setActiveItemClass(e){const t=this,n=t.control.querySelector(".last-active");n&&vs(n,"last-active"),ms(e,"active last-active"),t.trigger("item_select",e),-1==t.activeItems.indexOf(e)&&t.activeItems.push(e)}removeActiveItem(e){var t=this.activeItems.indexOf(e);this.activeItems.splice(t,1),vs(e,"active")}clearActiveItems(){vs(this.activeItems,"active"),this.activeItems=[]}setActiveOption(e,t=!0){e!==this.activeOption&&(this.clearActiveOption(),e&&(this.activeOption=e,Os(this.focus_node,{"aria-activedescendant":e.getAttribute("id")}),Os(e,{"aria-selected":"true"}),ms(e,"active"),t&&this.scrollToOption(e)))}scrollToOption(e,t){if(!e)return;const n=this.dropdown_content,r=n.clientHeight,i=n.scrollTop||0,o=e.offsetHeight,s=e.getBoundingClientRect().top-n.getBoundingClientRect().top+i;s+o>r+i?this.scroll(s-r+o,t):s<i&&this.scroll(s,t)}scroll(e,t){const n=this.dropdown_content;t&&(n.style.scrollBehavior=t),n.scrollTop=e,n.style.scrollBehavior=""}clearActiveOption(){this.activeOption&&(vs(this.activeOption,"active"),Os(this.activeOption,{"aria-selected":null})),this.activeOption=null,Os(this.focus_node,{"aria-activedescendant":null})}selectAll(){const e=this;if("single"===e.settings.mode)return;const t=e.controlChildren();t.length&&(e.inputState(),e.close(),e.activeItems=t,ds(t,t=>{e.setActiveItemClass(t)}))}inputState(){var e=this;e.control.contains(e.control_input)&&(Os(e.control_input,{placeholder:e.settings.placeholder}),e.activeItems.length>0||!e.isFocused&&e.settings.hidePlaceholder&&e.items.length>0?(e.setTextboxValue(),e.isInputHidden=!0):(e.settings.hidePlaceholder&&e.items.length>0&&Os(e.control_input,{placeholder:""}),e.isInputHidden=!1),e.wrapper.classList.toggle("input-hidden",e.isInputHidden))}inputValue(){return this.control_input.value.trim()}focus(){var e=this;e.isDisabled||e.isReadOnly||(e.ignoreFocus=!0,e.control_input.offsetWidth?e.control_input.focus():e.focus_node.focus(),setTimeout(()=>{e.ignoreFocus=!1,e.onFocus()},0))}blur(){this.focus_node.blur(),this.onBlur()}getScoreFunction(e){return this.sifter.getScoreFunction(e,this.getSearchOptions())}getSearchOptions(){var e=this.settings,t=e.sortField;return"string"==typeof e.sortField&&(t=[{field:e.sortField}]),{fields:e.searchField,conjunction:e.searchConjunction,sort:t,nesting:e.nesting}}search(e){var t,n,r=this,i=this.getSearchOptions();if(r.settings.score&&"function"!=typeof(n=r.settings.score.call(r,e)))throw new Error('Tom Select "score" setting must be a function that returns a function');return e!==r.lastQuery?(r.lastQuery=e,t=r.sifter.search(e,Object.assign(i,{score:n})),r.currentResults=t):t=Object.assign({},r.currentResults),r.settings.hideSelected&&(t.items=t.items.filter(e=>{let t=es(e.id);return!(t&&-1!==r.items.indexOf(t))})),t}refreshOptions(e=!0){var t,n,r,i,o,s,a,l,c,u;const d={},p=[];var f=this,h=f.inputValue();const g=h===f.lastQuery||""==h&&null==f.lastQuery;var m=f.search(h),v=null,y=f.settings.shouldOpen||!1,b=f.dropdown_content;g&&(v=f.activeOption)&&(c=v.closest("[data-group]")),i=m.items.length,"number"==typeof f.settings.maxOptions&&(i=Math.min(i,f.settings.maxOptions)),i>0&&(y=!0);const _=(e,t)=>{let n=d[e];if(void 0!==n){let e=p[n];if(void 0!==e)return[n,e.fragment]}let r=document.createDocumentFragment();return n=p.length,p.push({fragment:r,order:t,optgroup:e}),[n,r]};for(t=0;t<i;t++){let e=m.items[t];if(!e)continue;let i=e.id,a=f.options[i];if(void 0===a)continue;let l=ts(i),u=f.getOption(l,!0);for(f.settings.hideSelected||u.classList.toggle("selected",f.items.includes(l)),o=a[f.settings.optgroupField]||"",n=0,r=(s=Array.isArray(o)?o:[o])&&s.length;n<r;n++){o=s[n];let e=a.$order,t=f.optgroups[o];void 0===t?o="":e=t.$order;const[r,l]=_(o,e);n>0&&(u=u.cloneNode(!0),Os(u,{id:a.$id+"-clone-"+n,"aria-selected":null}),u.classList.add("ts-cloned"),vs(u,"active"),f.activeOption&&f.activeOption.dataset.value==i&&c&&c.dataset.group===o.toString()&&(v=u)),l.appendChild(u),""!=o&&(d[o]=r)}}var w;f.settings.lockOptgroupOrder&&p.sort((e,t)=>e.order-t.order),a=document.createDocumentFragment(),ds(p,e=>{let t=e.fragment,n=e.optgroup;if(!t||!t.children.length)return;let r=f.optgroups[n];if(void 0!==r){let e=document.createDocumentFragment(),n=f.render("optgroup_header",r);us(e,n),us(e,t);let i=f.render("optgroup",{group:r,options:e});us(a,i)}else us(a,t)}),b.innerHTML="",us(b,a),f.settings.highlight&&(w=b.querySelectorAll("span.highlight"),Array.prototype.forEach.call(w,function(e){var t=e.parentNode;t.replaceChild(e.firstChild,e),t.normalize()}),m.query.length&&m.tokens.length&&ds(m.tokens,e=>{Ss(b,e.regex)}));var x=e=>{let t=f.render(e,{input:h});return t&&(y=!0,b.insertBefore(t,b.firstChild)),t};if(f.loading?x("loading"):f.settings.shouldLoad.call(f,h)?0===m.items.length&&x("no_results"):x("not_loading"),(l=f.canCreate(h))&&(u=x("option_create")),f.hasOptions=m.items.length>0||l,y){if(m.items.length>0){if(v||"single"!==f.settings.mode||null==f.items[0]||(v=f.getOption(f.items[0])),!b.contains(v)){let e=0;u&&!f.settings.addPrecedence&&(e=1),v=f.selectable()[e]}}else u&&(v=u);e&&!f.isOpen&&(f.open(),f.scrollToOption(v,"auto")),f.setActiveOption(v)}else f.clearActiveOption(),e&&f.isOpen&&f.close(!1)}selectable(){return this.dropdown_content.querySelectorAll("[data-selectable]")}addOption(e,t=!1){const n=this;if(Array.isArray(e))return n.addOptions(e,t),!1;const r=es(e[n.settings.valueField]);return null!==r&&!n.options.hasOwnProperty(r)&&(e.$order=e.$order||++n.order,e.$id=n.inputId+"-opt-"+e.$order,n.options[r]=e,n.lastQuery=null,t&&(n.userOptions[r]=t,n.trigger("option_add",r,e)),r)}addOptions(e,t=!1){ds(e,e=>{this.addOption(e,t)})}registerOption(e){return this.addOption(e)}registerOptionGroup(e){var t=es(e[this.settings.optgroupValueField]);return null!==t&&(e.$order=e.$order||++this.order,this.optgroups[t]=e,t)}addOptionGroup(e,t){var n;t[this.settings.optgroupValueField]=e,(n=this.registerOptionGroup(t))&&this.trigger("optgroup_add",n,t)}removeOptionGroup(e){this.optgroups.hasOwnProperty(e)&&(delete this.optgroups[e],this.clearCache(),this.trigger("optgroup_remove",e))}clearOptionGroups(){this.optgroups={},this.clearCache(),this.trigger("optgroup_clear")}updateOption(e,t){const n=this;var r,i;const o=es(e),s=es(t[n.settings.valueField]);if(null===o)return;const a=n.options[o];if(null==a)return;if("string"!=typeof s)throw new Error("Value must be set in option data");const l=n.getOption(o),c=n.getItem(o);if(t.$order=t.$order||a.$order,delete n.options[o],n.uncacheValue(s),n.options[s]=t,l){if(n.dropdown_content.contains(l)){const e=n._render("option",t);As(l,e),n.activeOption===l&&n.setActiveOption(e)}l.remove()}c&&(-1!==(i=n.items.indexOf(o))&&n.items.splice(i,1,s),r=n._render("item",t),c.classList.contains("active")&&ms(r,"active"),As(c,r)),n.lastQuery=null}removeOption(e,t){const n=this;e=ts(e),n.uncacheValue(e),delete n.userOptions[e],delete n.options[e],n.lastQuery=null,n.trigger("option_remove",e),n.removeItem(e,t)}clearOptions(e){const t=(e||this.clearFilter).bind(this);this.loadedSearches={},this.userOptions={},this.clearCache();const n={};ds(this.options,(e,r)=>{t(e,r)&&(n[r]=e)}),this.options=this.sifter.items=n,this.lastQuery=null,this.trigger("option_clear")}clearFilter(e,t){return this.items.indexOf(t)>=0}getOption(e,t=!1){const n=es(e);if(null===n)return null;const r=this.options[n];if(null!=r){if(r.$div)return r.$div;if(t)return this._render("option",r)}return null}getAdjacent(e,t,n="option"){var r;if(!e)return null;r="item"==n?this.controlChildren():this.dropdown_content.querySelectorAll("[data-selectable]");for(let n=0;n<r.length;n++)if(r[n]==e)return t>0?r[n+1]:r[n-1];return null}getItem(e){if("object"==typeof e)return e;var t=es(e);return null!==t?this.control.querySelector(`[data-value="${cs(t)}"]`):null}addItems(e,t){var n=this,r=Array.isArray(e)?e:[e];const i=(r=r.filter(e=>-1===n.items.indexOf(e)))[r.length-1];r.forEach(e=>{n.isPending=e!==i,n.addItem(e,t)})}addItem(e,t){is(this,t?[]:["change","dropdown_close"],()=>{var n,r;const i=this,o=i.settings.mode,s=es(e);if((!s||-1===i.items.indexOf(s)||("single"===o&&i.close(),"single"!==o&&i.settings.duplicates))&&null!==s&&i.options.hasOwnProperty(s)&&("single"===o&&i.clear(t),"multi"!==o||!i.isFull())){if(n=i._render("item",i.options[s]),i.control.contains(n)&&(n=n.cloneNode(!0)),r=i.isFull(),i.items.splice(i.caretPos,0,s),i.insertAtCaret(n),i.isSetup){if(!i.isPending&&i.settings.hideSelected){let e=i.getOption(s),t=i.getAdjacent(e,1);t&&i.setActiveOption(t)}i.isPending||i.settings.closeAfterSelect||i.refreshOptions(i.isFocused&&"single"!==o),0!=i.settings.closeAfterSelect&&i.isFull()?i.close():i.isPending||i.positionDropdown(),i.trigger("item_add",s,n),i.isPending||i.updateOriginalInput({silent:t})}(!i.isPending||!r&&i.isFull())&&(i.inputState(),i.refreshState())}})}removeItem(e=null,t){const n=this;if(!(e=n.getItem(e)))return;var r,i;const o=e.dataset.value;r=xs(e),e.remove(),e.classList.contains("active")&&(i=n.activeItems.indexOf(e),n.activeItems.splice(i,1),vs(e,"active")),n.items.splice(r,1),n.lastQuery=null,!n.settings.persist&&n.userOptions.hasOwnProperty(o)&&n.removeOption(o,t),r<n.caretPos&&n.setCaret(n.caretPos-1),n.updateOriginalInput({silent:t}),n.refreshState(),n.positionDropdown(),n.trigger("item_remove",o,e)}createItem(e=null,t=()=>{}){3===arguments.length&&(t=arguments[2]),"function"!=typeof t&&(t=()=>{});var n,r=this,i=r.caretPos;if(e=e||r.inputValue(),!r.canCreate(e))return t(),!1;r.lock();var o=!1,s=e=>{if(r.unlock(),!e||"object"!=typeof e)return t();var n=es(e[r.settings.valueField]);if("string"!=typeof n)return t();r.setTextboxValue(),r.addOption(e,!0),r.setCaret(i),r.addItem(n),t(e),o=!0};return n="function"==typeof r.settings.create?r.settings.create.call(this,e,s):{[r.settings.labelField]:e,[r.settings.valueField]:e},o||s(n),!0}refreshItems(){var e=this;e.lastQuery=null,e.isSetup&&e.addItems(e.items),e.updateOriginalInput(),e.refreshState()}refreshState(){const e=this;e.refreshValidityState();const t=e.isFull(),n=e.isLocked;e.wrapper.classList.toggle("rtl",e.rtl);const r=e.wrapper.classList;var i;r.toggle("focus",e.isFocused),r.toggle("disabled",e.isDisabled),r.toggle("readonly",e.isReadOnly),r.toggle("required",e.isRequired),r.toggle("invalid",!e.isValid),r.toggle("locked",n),r.toggle("full",t),r.toggle("input-active",e.isFocused&&!e.isInputHidden),r.toggle("dropdown-active",e.isOpen),r.toggle("has-options",(i=e.options,0===Object.keys(i).length)),r.toggle("has-items",e.items.length>0)}refreshValidityState(){var e=this;e.input.validity&&(e.isValid=e.input.validity.valid,e.isInvalid=!e.isValid)}isFull(){return null!==this.settings.maxItems&&this.items.length>=this.settings.maxItems}updateOriginalInput(e={}){const t=this;var n,r;const i=t.input.querySelector('option[value=""]');if(t.is_select_tag){const o=[],s=t.input.querySelectorAll("option:checked").length;function a(e,n,r){return e||(e=ps('<option value="'+ns(n)+'">'+ns(r)+"</option>")),e!=i&&t.input.append(e),o.push(e),(e!=i||s>0)&&(e.selected=!0),e}t.input.querySelectorAll("option:checked").forEach(e=>{e.selected=!1}),0==t.items.length&&"single"==t.settings.mode?a(i,"",""):t.items.forEach(e=>{if(n=t.options[e],r=n[t.settings.labelField]||"",o.includes(n.$option)){a(t.input.querySelector(`option[value="${cs(e)}"]:not(:checked)`),e,r)}else n.$option=a(n.$option,e,r)})}else t.input.value=t.getValue();t.isSetup&&(e.silent||t.trigger("change",t.getValue()))}open(){var e=this;e.isLocked||e.isOpen||"multi"===e.settings.mode&&e.isFull()||(e.isOpen=!0,Os(e.focus_node,{"aria-expanded":"true"}),e.refreshState(),gs(e.dropdown,{visibility:"hidden",display:"block"}),e.positionDropdown(),gs(e.dropdown,{visibility:"visible",display:"block"}),e.focus(),e.trigger("dropdown_open",e.dropdown))}close(e=!0){var t=this,n=t.isOpen;e&&(t.setTextboxValue(),"single"===t.settings.mode&&t.items.length&&t.inputState()),t.isOpen=!1,Os(t.focus_node,{"aria-expanded":"false"}),gs(t.dropdown,{display:"none"}),t.settings.hideSelected&&t.clearActiveOption(),t.refreshState(),n&&t.trigger("dropdown_close",t.dropdown)}positionDropdown(){if("body"===this.settings.dropdownParent){var e=this.control,t=e.getBoundingClientRect(),n=e.offsetHeight+t.top+window.scrollY,r=t.left+window.scrollX;gs(this.dropdown,{width:t.width+"px",top:n+"px",left:r+"px"})}}clear(e){var t=this;if(t.items.length){var n=t.controlChildren();ds(n,e=>{t.removeItem(e,!0)}),t.inputState(),e||t.updateOriginalInput(),t.trigger("clear")}}insertAtCaret(e){const t=this,n=t.caretPos,r=t.control;r.insertBefore(e,r.children[n]||null),t.setCaret(n+1)}deleteSelection(e){var t,n,r,i,o,s=this;t=e&&8===e.keyCode?-1:1,n={start:(o=s.control_input).selectionStart||0,length:(o.selectionEnd||0)-(o.selectionStart||0)};const a=[];if(s.activeItems.length)i=ws(s.activeItems,t),r=xs(i),t>0&&r++,ds(s.activeItems,e=>a.push(e));else if((s.isFocused||"single"===s.settings.mode)&&s.items.length){const e=s.controlChildren();let r;t<0&&0===n.start&&0===n.length?r=e[s.caretPos-1]:t>0&&n.start===s.inputValue().length&&(r=e[s.caretPos]),void 0!==r&&a.push(r)}if(!s.shouldDelete(a,e))return!1;for(os(e,!0),void 0!==r&&s.setCaret(r);a.length;)s.removeItem(a.pop());return s.inputState(),s.positionDropdown(),s.refreshOptions(!1),!0}shouldDelete(e,t){const n=e.map(e=>e.dataset.value);return!(!n.length||"function"==typeof this.settings.onDelete&&!1===this.settings.onDelete(n,t))}advanceSelection(e,t){var n,r,i=this;i.rtl&&(e*=-1),i.inputValue().length||(as(Es,t)||as("shiftKey",t)?(r=(n=i.getLastActive(e))?n.classList.contains("active")?i.getAdjacent(n,e,"item"):n:e>0?i.control_input.nextElementSibling:i.control_input.previousElementSibling)&&(r.classList.contains("active")&&i.removeActiveItem(n),i.setActiveItemClass(r)):i.moveCaret(e))}moveCaret(e){}getLastActive(e){let t=this.control.querySelector(".last-active");if(t)return t;var n=this.control.querySelectorAll(".active");return n?ws(n,e):void 0}setCaret(e){this.caretPos=this.items.length}controlChildren(){return Array.from(this.control.querySelectorAll("[data-ts-item]"))}lock(){this.setLocked(!0)}unlock(){this.setLocked(!1)}setLocked(e=this.isReadOnly||this.isDisabled){this.isLocked=e,this.refreshState()}disable(){this.setDisabled(!0),this.close()}enable(){this.setDisabled(!1)}setDisabled(e){this.focus_node.tabIndex=e?-1:this.tabIndex,this.isDisabled=e,this.input.disabled=e,this.control_input.disabled=e,this.setLocked()}setReadOnly(e){this.isReadOnly=e,this.input.readOnly=e,this.control_input.readOnly=e,this.setLocked()}destroy(){var e=this,t=e.revertSettings;e.trigger("destroy"),e.off(),e.wrapper.remove(),e.dropdown.remove(),e.input.innerHTML=t.innerHTML,e.input.tabIndex=t.tabIndex,vs(e.input,"tomselected","ts-hidden-accessible"),e._destroy(),delete e.input.tomselect}render(e,t){var n,r;const i=this;if("function"!=typeof this.settings.render[e])return null;if(!(r=i.settings.render[e].call(this,t,ns)))return null;if(r=ps(r),"option"===e||"option_create"===e?t[i.settings.disabledField]?Os(r,{"aria-disabled":"true"}):Os(r,{"data-selectable":""}):"optgroup"===e&&(n=t.group[i.settings.optgroupValueField],Os(r,{"data-group":n}),t.group[i.settings.disabledField]&&Os(r,{"data-disabled":""})),"option"===e||"item"===e){const n=ts(t[i.settings.valueField]);Os(r,{"data-value":n}),"item"===e?(ms(r,i.settings.itemClass),Os(r,{"data-ts-item":""})):(ms(r,i.settings.optionClass),Os(r,{role:"option",id:t.$id}),t.$div=r,i.options[n]=t)}return r}_render(e,t){const n=this.render(e,t);if(null==n)throw"HTMLElement expected";return n}clearCache(){ds(this.options,e=>{e.$div&&(e.$div.remove(),delete e.$div)})}uncacheValue(e){const t=this.getOption(e);t&&t.remove()}canCreate(e){return this.settings.create&&e.length>0&&this.settings.createFilter.call(this,e)}hook(e,t,n){var r=this,i=r[t];r[t]=function(){var t,o;return"after"===e&&(t=i.apply(r,arguments)),o=n.apply(r,arguments),"instead"===e?o:("before"===e&&(t=i.apply(r,arguments)),t)}}}const js=e=>"boolean"==typeof e?e?"1":"0":e+"",Is=(e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())},Ds=e=>"string"==typeof e&&e.indexOf("<")>-1;const Ps=e=>"string"==typeof e&&e.indexOf("<")>-1;const Ns=(e,t,n,r)=>{e.addEventListener(t,n,r)},Fs=e=>"string"==typeof e&&e.indexOf("<")>-1,Ms=(e,t)=>{((e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)})(t,(t,n)=>{null==t?e.removeAttribute(n):e.setAttribute(n,""+t)})};const $s=e=>"string"==typeof e&&e.indexOf("<")>-1;const qs=e=>{var t=[];return((e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)})(e,e=>{"string"==typeof e&&(e=e.trim().split(/[\t\n\f\r\s]/)),Array.isArray(e)&&(t=t.concat(e))}),t.filter(Boolean)},Hs=e=>(Array.isArray(e)||(e=[e]),e);const Rs=e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if(Bs(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)},Bs=e=>"string"==typeof e&&e.indexOf("<")>-1,Ws=e=>{var t=[];return((e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)})(e,e=>{"string"==typeof e&&(e=e.trim().split(/[\t\n\f\r\s]/)),Array.isArray(e)&&(t=t.concat(e))}),t.filter(Boolean)},zs=e=>(Array.isArray(e)||(e=[e]),e);const Vs=(e,t,n,r)=>{e.addEventListener(t,n,r)};const Us=(e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())},Xs=(e,t,n,r)=>{e.addEventListener(t,n,r)},Ks=e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if(Gs(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)},Gs=e=>"string"==typeof e&&e.indexOf("<")>-1;const Qs=e=>{var t=[];return((e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)})(e,e=>{"string"==typeof e&&(e=e.trim().split(/[\t\n\f\r\s]/)),Array.isArray(e)&&(t=t.concat(e))}),t.filter(Boolean)},Ys=e=>(Array.isArray(e)||(e=[e]),e);Ls.define("change_listener",function(){var e,t,n,r;e=this.input,t="change",n=()=>{this.sync()},e.addEventListener(t,n,r)}),Ls.define("checkbox_options",function(e){var t=this,n=t.onOptionSelect;t.settings.hideSelected=!1;const r=Object.assign({className:"tomselect-checkbox",checkedClassNames:void 0,uncheckedClassNames:void 0},e);var i=function(e,t){t?(e.checked=!0,r.uncheckedClassNames&&e.classList.remove(...r.uncheckedClassNames),r.checkedClassNames&&e.classList.add(...r.checkedClassNames)):(e.checked=!1,r.checkedClassNames&&e.classList.remove(...r.checkedClassNames),r.uncheckedClassNames&&e.classList.add(...r.uncheckedClassNames))},o=function(e){setTimeout(()=>{var t=e.querySelector("input."+r.className);t instanceof HTMLInputElement&&i(t,e.classList.contains("selected"))},1)};t.hook("after","setupTemplates",()=>{var e=t.settings.render.option;t.settings.render.option=(n,o)=>{var s=(e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if(Ds(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)})(e.call(t,n,o)),a=document.createElement("input");r.className&&a.classList.add(r.className),a.addEventListener("click",function(e){Is(e)}),a.type="checkbox";const l=null==(c=n[t.settings.valueField])?null:js(c);var c;return i(a,!!(l&&t.items.indexOf(l)>-1)),s.prepend(a),s}}),t.on("item_remove",e=>{var n=t.getOption(e);n&&(n.classList.remove("selected"),o(n))}),t.on("item_add",e=>{var n=t.getOption(e);n&&o(n)}),t.hook("instead","onOptionSelect",(e,r)=>{if(r.classList.contains("selected"))return r.classList.remove("selected"),t.removeItem(r.dataset.value),t.refreshOptions(),void Is(e,!0);n.call(t,e,r),o(r)})}),Ls.define("clear_button",function(e){const t=this,n=Object.assign({className:"clear-button",title:"Clear All",html:e=>`<div class="${e.className}" title="${e.title}">&#10799;</div>`},e);t.on("initialize",()=>{var e=(e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if(Ps(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)})(n.html(n));e.addEventListener("click",e=>{t.isLocked||(t.clear(),"single"===t.settings.mode&&t.settings.allowEmptyOption&&t.addItem(""),e.preventDefault(),e.stopPropagation())}),t.control.appendChild(e)})}),Ls.define("drag_drop",function(){var e=this;if("multi"!==e.settings.mode)return;var t=e.lock,n=e.unlock;let r,i=!0;e.hook("after","setupTemplates",()=>{var t=e.settings.render.item;e.settings.render.item=(n,o)=>{const s=(e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if(Fs(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)})(t.call(e,n,o));Ms(s,{draggable:"true"});const a=e=>{e.preventDefault(),s.classList.add("ts-drag-over"),l(s,r)},l=(e,t)=>{var n,r,i;void 0!==t&&(((e,t)=>{do{var n;if(e==(t=null==(n=t)?void 0:n.previousElementSibling))return!0}while(t&&t.previousElementSibling);return!1})(t,s)?(r=t,null==(i=(n=e).parentNode)||i.insertBefore(r,n.nextSibling)):((e,t)=>{var n;null==(n=e.parentNode)||n.insertBefore(t,e)})(e,t))};return Ns(s,"mousedown",e=>{i||((e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())})(e),e.stopPropagation()}),Ns(s,"dragstart",e=>{r=s,setTimeout(()=>{s.classList.add("ts-dragging")},0)}),Ns(s,"dragenter",a),Ns(s,"dragover",a),Ns(s,"dragleave",()=>{s.classList.remove("ts-drag-over")}),Ns(s,"dragend",()=>{var t;document.querySelectorAll(".ts-drag-over").forEach(e=>e.classList.remove("ts-drag-over")),null==(t=r)||t.classList.remove("ts-dragging"),r=void 0;var n=[];e.control.querySelectorAll("[data-value]").forEach(e=>{if(e.dataset.value){let t=e.dataset.value;t&&n.push(t)}}),e.setValue(n)}),s}}),e.hook("instead","lock",()=>(i=!1,t.call(e))),e.hook("instead","unlock",()=>(i=!0,n.call(e)))}),Ls.define("dropdown_header",function(e){const t=this,n=Object.assign({title:"Untitled",headerClass:"dropdown-header",titleRowClass:"dropdown-header-title",labelClass:"dropdown-header-label",closeClass:"dropdown-header-close",html:e=>'<div class="'+e.headerClass+'"><div class="'+e.titleRowClass+'"><span class="'+e.labelClass+'">'+e.title+'</span><a class="'+e.closeClass+'">&times;</a></div></div>'},e);t.on("initialize",()=>{var e=(e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if($s(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)})(n.html(n)),r=e.querySelector("."+n.closeClass);r&&r.addEventListener("click",e=>{((e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())})(e,!0),t.close()}),t.dropdown.insertBefore(e,t.dropdown.firstChild)})}),Ls.define("caret_position",function(){var e=this;e.hook("instead","setCaret",t=>{"single"!==e.settings.mode&&e.control.contains(e.control_input)?(t=Math.max(0,Math.min(e.items.length,t)))==e.caretPos||e.isPending||e.controlChildren().forEach((n,r)=>{r<t?e.control_input.insertAdjacentElement("beforebegin",n):e.control.appendChild(n)}):t=e.items.length,e.caretPos=t}),e.hook("instead","moveCaret",t=>{if(!e.isFocused)return;const n=e.getLastActive(t);if(n){const r=((e,t)=>{if(!e)return-1;t=t||e.nodeName;for(var n=0;e=e.previousElementSibling;)e.matches(t)&&n++;return n})(n);e.setCaret(t>0?r+1:r),e.setActiveItem(),((e,...t)=>{var n=qs(t);(e=Hs(e)).map(e=>{n.map(t=>{e.classList.remove(t)})})})(n,"last-active")}else e.setCaret(e.caretPos+t)})}),Ls.define("dropdown_input",function(){const e=this;e.settings.shouldOpen=!0,e.hook("before","setup",()=>{e.focus_node=e.control,((e,...t)=>{var n=Ws(t);(e=zs(e)).map(e=>{n.map(t=>{e.classList.add(t)})})})(e.control_input,"dropdown-input");const t=Rs('<div class="dropdown-input-wrap">');t.append(e.control_input),e.dropdown.insertBefore(t,e.dropdown.firstChild);const n=Rs('<input class="items-placeholder" tabindex="-1" />');n.placeholder=e.settings.placeholder||"",e.control.append(n)}),e.on("initialize",()=>{e.control_input.addEventListener("keydown",t=>{switch(t.keyCode){case 27:return e.isOpen&&(((e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())})(t,!0),e.close()),void e.clearActiveItems();case 9:e.focus_node.tabIndex=-1}return e.onKeyDown.call(e,t)}),e.on("blur",()=>{e.focus_node.tabIndex=e.isDisabled?-1:e.tabIndex}),e.on("dropdown_open",()=>{e.control_input.focus()});const t=e.onBlur;var n,r,i,o;e.hook("instead","onBlur",n=>{if(!n||n.relatedTarget!=e.control_input)return t.call(e)}),n=e.control_input,r="blur",i=()=>e.onBlur(),n.addEventListener(r,i,o),e.hook("before","close",()=>{e.isOpen&&e.focus_node.focus({preventScroll:!0})})})}),Ls.define("input_autogrow",function(){var e=this;e.on("initialize",()=>{var t=document.createElement("span"),n=e.control_input;t.style.cssText="position:absolute; top:-99999px; left:-99999px; width:auto; padding:0; white-space:pre; ",e.wrapper.appendChild(t);for(const e of["letterSpacing","fontSize","fontFamily","fontWeight","textTransform"])t.style[e]=n.style[e];var r=()=>{t.textContent=n.value,n.style.width=t.clientWidth+"px"};r(),e.on("update item_add item_remove",r),Vs(n,"input",r),Vs(n,"keyup",r),Vs(n,"blur",r),Vs(n,"update",r)})}),Ls.define("no_backspace_delete",function(){var e=this,t=e.deleteSelection;this.hook("instead","deleteSelection",n=>!!e.activeItems.length&&t.call(e,n))}),Ls.define("no_active_items",function(){this.hook("instead","setActiveItem",()=>{}),this.hook("instead","selectAll",()=>{})}),Ls.define("optgroup_columns",function(){var e=this,t=e.onKeyDown;e.hook("instead","onKeyDown",n=>{var r,i,o,s;if(!e.isOpen||37!==n.keyCode&&39!==n.keyCode)return t.call(e,n);e.ignoreHover=!0,s=((e,t)=>{for(;e&&e.matches;){if(e.matches(t))return e;e=e.parentNode}})(e.activeOption,"[data-group]"),r=((e,t)=>{if(!e)return-1;t=t||e.nodeName;for(var n=0;e=e.previousElementSibling;)e.matches(t)&&n++;return n})(e.activeOption,"[data-selectable]"),s&&(s=37===n.keyCode?s.previousSibling:s.nextSibling)&&(i=(o=s.querySelectorAll("[data-selectable]"))[Math.min(o.length-1,r)])&&e.setActiveOption(i)})}),Ls.define("remove_button",function(e){const t=Object.assign({label:"&times;",title:"Remove",className:"remove",append:!0},e);var n=this;if(t.append){var r='<a href="javascript:void(0)" class="'+t.className+'" tabindex="-1" title="'+((t.title+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")+'">')+t.label+"</a>";n.hook("after","setupTemplates",()=>{var e=n.settings.render.item;n.settings.render.item=(t,i)=>{var o=Ks(e.call(n,t,i)),s=Ks(r);return o.appendChild(s),Xs(s,"mousedown",e=>{Us(e,!0)}),Xs(s,"click",e=>{n.isLocked||(Us(e,!0),n.isLocked||n.shouldDelete([o],e)&&(n.removeItem(o),n.refreshOptions(!1),n.inputState()))}),o}})}}),Ls.define("restore_on_backspace",function(e){const t=this,n=Object.assign({text:e=>e[t.settings.labelField]},e);t.on("item_remove",function(e){if(t.isFocused&&""===t.control_input.value.trim()){var r=t.options[e];r&&t.setTextboxValue(n.text.call(t,r))}})}),Ls.define("virtual_scroll",function(){const e=this,t=e.canLoad,n=e.clearActiveOption,r=e.loadCallback;var i,o,s={},a=!1,l=[];if(e.settings.shouldLoadMore||(e.settings.shouldLoadMore=()=>{if(i.clientHeight/(i.scrollHeight-i.scrollTop)>.9)return!0;if(e.activeOption){var t=e.selectable();if(Array.from(t).indexOf(e.activeOption)>=t.length-2)return!0}return!1}),!e.settings.firstUrl)throw"virtual_scroll plugin requires a firstUrl() method";e.settings.sortField=[{field:"$order"},{field:"$score"}];const c=t=>!("number"==typeof e.settings.maxOptions&&i.children.length>=e.settings.maxOptions)&&!(!(t in s)||!s[t]),u=(t,n)=>e.items.indexOf(n)>=0||l.indexOf(n)>=0;e.setNextUrl=(e,t)=>{s[e]=t},e.getUrl=t=>{if(t in s){const e=s[t];return s[t]=!1,e}return e.clearPagination(),e.settings.firstUrl.call(e,t)},e.clearPagination=()=>{s={}},e.hook("instead","clearActiveOption",()=>{if(!a)return n.call(e)}),e.hook("instead","canLoad",n=>n in s?c(n):t.call(e,n)),e.hook("instead","loadCallback",(t,n)=>{if(a){if(o){const n=t[0];void 0!==n&&(o.dataset.value=n[e.settings.valueField])}}else e.clearOptions(u);r.call(e,t,n),a=!1}),e.hook("after","refreshOptions",()=>{const t=e.lastValue;var n;c(t)?(n=e.render("loading_more",{query:t}))&&(n.setAttribute("data-selectable",""),o=n):t in s&&!i.querySelector(".no-results")&&(n=e.render("no_more_results",{query:t})),n&&(((e,...t)=>{var n=Qs(t);(e=Ys(e)).map(e=>{n.map(t=>{e.classList.add(t)})})})(n,e.settings.optionClass),i.append(n))}),e.on("initialize",()=>{l=Object.keys(e.options),i=e.dropdown_content,e.settings.render=Object.assign({},{loading_more:()=>'<div class="loading-more-results">Loading more results ... </div>',no_more_results:()=>'<div class="no-more-results">No more results</div>'},e.settings.render),i.addEventListener("scroll",()=>{e.settings.shouldLoadMore.call(e)&&c(e.lastValue)&&(a||(a=!0,e.load.call(e,e.lastValue)))})})});const Js=Ls;function Zs(e){return function(e){if(Array.isArray(e))return ea(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return ea(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ea(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ea(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}globalThis.$=globalThis.jQuery=i(),globalThis.bootstrap=t,globalThis.TomSelect=Js,globalThis.FOSSBilling={message:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",n=document.querySelector(".toast-container");if(n){var r="toast-"+Date.now(),i="error"===t?"bg-danger":"success"===t?"bg-success":"warning"===t?"bg-warning":"bg-info",o='\n            <div id="'.concat(r,'" class="toast ').concat(i,' text-white" role="alert" aria-live="assertive" aria-atomic="true">\n                <div class="toast-header">\n                    <strong class="me-auto">').concat(t.charAt(0).toUpperCase()+t.slice(1),'</strong>\n                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>\n                </div>\n                <div class="toast-body">\n                    ').concat(e,"\n                </div>\n            </div>\n        ");n.insertAdjacentHTML("beforeend",o);var s=document.getElementById(r);new _o(s,{delay:5e3}).show(),s.addEventListener("hidden.bs.toast",function(){s.remove()})}}},globalThis.bb={reload:function(){window.location.reload()},redirect:function(e){window.location.href=e}},document.addEventListener("DOMContentLoaded",function(){Zs(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map(function(e){return new bi(e)}),document.querySelectorAll(".js-language-selector, .tom-select").forEach(function(e){e.tomselect||new Js(e,{plugins:["dropdown_header"],render:{option:function(e,t){var n;return'<div class="d-flex align-items-center"><span class="fi fi-'+t((null===(n=e.value.split("_")[1])||void 0===n?void 0:n.toLowerCase())||"")+' me-2"></span><span>'+t(e.text)+"</span></div>"},item:function(e,t){var n;return'<div class="d-flex align-items-center"><span class="fi fi-'+t((null===(n=e.value.split("_")[1])||void 0===n?void 0:n.toLowerCase())||"")+' me-2"></span><span>'+t(e.text)+"</span></div>"}}})}),globalThis.flashMessage=function(e){var t=e.message,n=void 0===t?"":t,r=e.reload,i=void 0!==r&&r,o=e.type,s=void 0===o?"info":o,a="flash-message",l=sessionStorage.getItem(a);if(""===n&&l)return FOSSBilling.message(l,s),void sessionStorage.removeItem(a);n&&(sessionStorage.setItem(a,n),"boolean"==typeof i&&i?bb.reload():"string"==typeof i&&bb.redirect(i))},flashMessage({}),document.querySelectorAll("input[required], textarea[required]").forEach(function(e){var t=e.previousElementSibling;if(!e.parentElement.parentElement.classList.contains("auth")&&t&&"label"===t.tagName.toLowerCase()){var n=document.createElement("span");n.textContent=" *",n.classList.add("text-danger"),t.appendChild(n)}}),document.querySelectorAll("select.currency_selector").forEach(function(e){e.addEventListener("change",function(){"undefined"!=typeof API&&API.guest&&API.guest.post("cart/set_currency",{currency:e.value},function(e){location.reload()},function(e){FOSSBilling.message(e,"error")})})});var e=document.getElementById("period-selector");e&&(e.addEventListener("change",function(){var e=this.value;document.querySelectorAll(".period").forEach(function(e){e.style.display="none"}),document.querySelectorAll(".period."+e).forEach(function(e){e.style.display="block"})}),e.dispatchEvent(new Event("change"))),document.querySelectorAll("form").forEach(function(e){e.addEventListener("submit",function(t){var n=e.querySelectorAll("[required]"),r=!0;n.forEach(function(e){e.value.trim()?e.classList.remove("is-invalid"):(e.classList.add("is-invalid"),r=!1)}),r||(t.preventDefault(),FOSSBilling.message("Please fill in all required fields","error"))})})})})()})();