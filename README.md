# WIDDX Theme for FOSSBilling

A modern, responsive theme for FOSSBilling built with Bootstrap 5 and modern web technologies.

## Features

- **Bootstrap 5.3.7** - Modern CSS framework with responsive design
- **Tom-Select** - Enhanced select components with search functionality
- **Flag Icons** - Country flag icons for language selection
- **Responsive Design** - Mobile-first approach with proper mobile navigation
- **Accessibility** - ARIA labels, keyboard navigation, and screen reader support
- **Modern Build System** - Webpack Encore for asset compilation
- **SCSS Support** - Customizable styling with Sass preprocessing
- **Toast Notifications** - Modern notification system
- **Form Validation** - Client-side validation with Bootstrap styling

## Installation

1. Extract the theme to your FOSSBilling themes directory
2. Install dependencies: `npm install`
3. Build assets: `npm run build`
4. Activate the theme in FOSSBilling admin panel

## Development

### Build Commands

- `npm run dev` - Build for development
- `npm run watch` - Watch for changes and rebuild
- `npm run build` - Build for production
- `npm run dev-server` - Start development server

### File Structure

```
widdx/
├── assets/
│   ├── scss/
│   │   ├── widdx.scss      # Main stylesheet
│   │   └── _flags.scss     # Flag icon styles
│   └── widdx.js            # Main JavaScript file
├── config/
│   └── settings.html.twig  # Theme configuration
├── html/
│   ├── layout_default.html.twig    # Main layout
│   ├── layout_public.html.twig     # Public layout
│   ├── partial_*.html.twig         # Reusable components
│   └── macro_functions.html.twig   # Twig macros
├── html_custom/            # User customizations
├── manifest.json           # Theme metadata
├── package.json           # Node.js dependencies
└── webpack.config.js      # Build configuration
```

## Customization

### Theme Settings

The theme includes comprehensive settings accessible through the FOSSBilling admin panel:

- **Look & Feel**: Theme variant selection (light/dark)
- **Header Items**: Logo and balance display options
- **Menu Configuration**: Top menu item visibility
- **Footer Links**: Up to 5 customizable footer links
- **Meta Settings**: SEO and social media configuration
- **JavaScript Injection**: Custom JavaScript code

### Custom Styling

1. Create custom SCSS files in `assets/scss/`
2. Import them in `widdx.scss`
3. Run `npm run build` to compile

### Custom Templates

1. Copy templates from `html/` to `html_custom/`
2. Modify as needed - custom templates take precedence

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This theme is released under the same license as FOSSBilling.

## Support

For support and documentation, visit the FOSSBilling documentation at https://fossbilling.org/docs
