<div class="card">
    <div class="card-body">
        <div class="pricing-section">
            {% if pricing.type == 'free' %}
                {% if product.allow_quantity_select %}
                    <div class="mb-3">
                        <label class="form-label">{{ 'Quantity:'|trans }}</label>
                        <input type="number" name="quantity" value="1" class="form-control" style="width: 80px;" min="1"/>
                    </div>
                    <hr/>
                {% endif %}

                <p class="fw-bold">{{ 'Total price:'|trans }}</p>
                <div class="h4 text-success">{{ 0 | money_convert }}</div>
            {% endif %}

            {% if pricing.type == 'once'  %}
                {% if product.allow_quantity_select %}
                    <div class="mb-3">
                        <label class="form-label">{{ 'Quantity:'|trans }}</label>
                        <input type="number" name="quantity" value="1" class="form-control" style="width: 80px;" min="1"/>
                    </div>
                    <hr/>
                {% endif %}

                <p class="fw-bold">{{ 'Total price:'|trans }}</p>
                <div class="h4 text-primary">{{ (pricing.once.price + pricing.once.setup) | money_convert }}</div>
            {% endif %}


            {% if pricing.type == 'recurrent'  %}
                {% if product.allow_quantity_select %}
                    <div class="mb-3">
                        <label class="form-label">{{ 'Quantity:'|trans }}</label>
                        <input type="number" name="quantity" value="1" class="form-control" style="width: 80px;" min="1"/>
                    </div>
                    <hr/>
                {% endif %}

                <div class="mb-3">
                    <label for="period-selector" class="form-label">{{ 'Billing Period:'|trans }}</label>
                    <select name="period" id="period-selector" class="form-select">
                        {% for code,prices in pricing.recurrent %}
                            {% if prices.enabled %}
                                <option value="{{code}}"{% if request.period == code %} selected="selected"{% endif %}>{{ periods[code] }}</option>
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>

                {% for code,prices in pricing.recurrent %}
                    {% if prices.enabled %}
                        {% if prices.setup > 0 %}
                            <div class="period {{code}} mb-3" style="display: none;">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <tr>
                                            <td>{{ 'Price'|trans }}</td>
                                            <td class="text-end">{{ prices.price | money_convert }}</td>
                                        </tr>
                                        <tr>
                                            <td>{{ 'Setup Price'|trans }}</td>
                                            <td class="text-end">{{ prices.setup | money_convert }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        {% endif %}
                    {% endif %}
                {% endfor %}

                <hr/>
                <p class="fw-bold">{{ 'Total price:'|trans }}</p>
                {% for code,prices in pricing.recurrent %}
                    <div class="h4 text-primary period {{code}}">{{ (prices.price + prices.setup) | money_convert }}</div>
                {% endfor %}
            {% endif %}
        </div>

        <div class="d-grid gap-2 mt-3">
            {% if product.addons|length > 0 %}
                <button class="btn btn-primary btn-lg" type="submit" id="order-button">{{ 'Continue'|trans }}</button>
            {% else %}
                <button class="btn btn-primary btn-lg" type="submit" id="order-button">{{ 'Order now'|trans }}</button>
            {% endif %}
        </div>
    </div>
</div>